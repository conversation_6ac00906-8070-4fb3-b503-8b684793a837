local QBCore = exports['qb-core']:GetCoreObject()

QBCore.Functions.CreateCallback('rpemotes:server:checkDiscordRoles', function(source, cb)
    local src = source
    local hasSilver = exports.zdiscord:isRolePresent(src, '1347445310766452803')
    local hasGold = exports.zdiscord:isRolePresent(src, '1347445310766452805')
    local hasPlatinum = exports.zdiscord:isRolePresent(src, '1347445310766452806')
    local hasDiamond = exports.zdiscord:isRolePresent(src, '1347445310766452807')

    -- Check for nil values and set them to false
    hasSilver = hasSilver ~= nil and hasSilver or false
    hasGold = hasGold ~= nil and hasGold or false
    hasPlatinum = hasPlatinum ~= nil and hasPlatinum or false
    hasDiamond = hasDiamond ~= nil and hasDiamond or false
    hasAmethyst = hasAmethyst ~= nil and hasAmethyst or false

    local result = {
        silver = hasSilver,
        gold = hasGold,
        platinum = hasPlatinum,
        diamond = hasDiamond,
        amethyst = hasAmethyst
    }

    cb(result)
end)
