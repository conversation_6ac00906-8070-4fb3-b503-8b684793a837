Locales['ru'] = {
 -- Russian 🇷🇺 by denrulezz
        ['emotes'] = "~h~~p~ Эмоции 🎬",
        ['danceemotes'] = "~h~~p~ 🕺 Танцевальные эмоции",
        ['animalemotes'] = "~h~~p~ 🐩 Эмоции животных",
        ['propemotes'] = "~h~~p~ 📦 Эмоции с пропом",
        ['favoriteemotes'] = "~h~~y~ 🌟 Избранные",
        ['favoriteinfo'] = "Выберите здесь эмоцию, чтобы сделать ее избранной.",
        ['rfavorite'] = "Сбросить избранное",
        ['prop2info'] = "❓ Эмоции с пропом могут быть расположены в конце",
        ['set'] = "Установить (",
        ['setboundemote'] = ") как привязанную эмоцию?",
        ['newsetemote'] = "~w~ теперь привязанная эмоция, нажмите ~g~CapsLock~w~ для использования.",
        ['cancelemote'] = "~h~~r~ Остановить эмоцию 🚷",
        ['cancelemoteinfo'] = "~r~X~w~ Отменяет воспроизводимую эмоцию",
        ['walkingstyles'] = "~h~~p~ Стили ходьбы 🚶🏻‍♂️",
        ['resetdef'] = "Сбросить на обычную",
        ['normalreset'] = "~h~~r~ Обычная (Сбросить)",
        ['moods'] = "~h~~p~ Настроение 😒",
        ['infoupdate'] = "Признательность 🤝🏻",
        ['remove_emote_keybind'] = 'Удаление эмоции из привязок клавиш',
        ['show_emote_keybind'] = 'просмотр эмоций на сочетаниях клавиш',
        ['play_emote'] = 'воспроизвести анимацию',
        ['open_menu_emote'] = 'Открыть меню анимаций',
        ['show_list_emote'] = 'Посмотреть список возможных эмоций',
        ['link_emote_keybind'] = 'Привязка эмоции к клавише',
        ['help_command'] = 'dance, camera, sit или любая другая эмоция',
        ['help_variation'] = '(Необязательно) 1, 2, 3 или любое число. Изменится текстура некоторых аксессуаров, используемых в эмоциях, например цвет телефона. Введите -1, чтобы увидеть список вариантов.',
        ['infoupdateav'] = "Информация (Доступно обновление)",
        ['infoupdateavtext'] = "Доступно обновление, загрузите последнюю версию с ~y~https://github.com/alberttheprince/rpemotes-reborn~w~",
        ['suggestions'] = "Предложения?",
        ['suggestionsinfo'] = "~r~Noor_Nahas~s~ на форумах FiveM для любых предложений по функциям/эмоциям! ✉️",
        ['notvaliddance'] = "не существующий танец.",
        ['notvalidemote'] = "не существующая эмоция.",
        ['nocancel'] = "Нет эмоций для отмены.",
        ['maleonly'] = "Эта эмоция только для мужчин, извините!",
        ['emotemenucmd'] = "Используйте команду /emotemenu, чтобы открыть меню анимации.",
        ['shareemotes'] = "~h~~p~ 👫 Совместные эмоции",
        ['shareemotesinfo'] = "Пригласите человека рядом для воспроизведения эмоции",
        ['sharedanceemotes'] = "~h~~p~ 🕺 Совместные танцы",
        ['notvalidsharedemote'] = "не существующая совместная эмоция.",
        ['sentrequestto'] = "Отправлен запрос ~y~",
        ['nobodyclose'] = "Никого ~r~close~w~ нет поблизости.",
        ['doyouwanna'] = "~y~Y~w~ для принятия, ~r~L~w~ для отмены (~g~",
        ['refuseemote'] = "Эмоция откленена.",
        ['makenearby'] = "заставляет ближайшего игрока играть",
        ['useleafblower'] = "Нажмите ~y~G~w~ для использование воздуходувки.",
        ['camera'] = "Нажмите ~y~G~w~ для вспышки.",
        ['makeitrain'] = "Нажмите ~y~G~w~ для дождя.",
        ['pee'] = "Удерживайте ~y~G~w~ чтоб писать.",
        ['spraychamp'] = "Удерживайте ~y~G~w~ для спрея шампанского",
        ['stun'] = "Нажмите ~y~G~w~ ,чтобы 'использовать' шокер.",
        ['smoke'] = "Press ~y~G~w~ to smoke.",
        ['vape'] = "Нажмите ~y~G~w~ чтобы вейпить.",
        ['candle'] = "press ~y~G~w~ to light candle.",
        ['boundto'] = "Связать (~y~%s~w~) для ~g~%s~w~",
        ['currentlyboundemotes'] = " Текущие привязанные эмоции:",
        ['notvalidkey'] = "недопустимая клавиша.",
        ['keybinds'] = "🔢 Бинды клавиш",
        ['keybindsinfo'] = "Использовать",
        ['searchemotes'] = "~h~~y~ 🔍 Поиск эмоции",
        ['searchinputtitle'] = "Поиск:",
        ['searchmenudesc'] = "результат(ы) для",
        ['searchnoresult'] = "Нет результатов по поиску",
        ['searchshifttofav'] = "Удерживайте L-Shift и нажмите Enter, чтобы установить в избранное.",
        ['searchcantsetfav'] = "Парные эмоции не могут быть добавлены в избранное.",
        ['invalidvariation'] = "Недопустимая вариация текстуры. Допустимые варианты: %s",
        ['firework'] = "Нажмите ~y~G~w~, чтобы запустить фейерверк.", 
        ['poop'] = "Нажмите ~y~G~w~, чтобы какать",
        ['puke'] = "Нажмите ~y~G~w~ для рвоты",
	['cut'] = "Нажмите ~y~G~w~ для резки",
        ['btn_select'] = "Выбрать",
        ['btn_back'] = "Назад",
        ['btn_switch'] = "Движение",
        ['btn_increment'] = "Приращение",
        ['dead'] = "Вы не можете использовать эмоции, когда умерли!",
        ['swimming'] = "Вы не можете использовать эмоции во время плавания.",
        ['notvalidpet'] = "ГАВ ГАВ! Эмоции для 4-ногих друзей 🐕!",
        ['animaldisabled'] = "Извините! Эмоции животных отключены на сервере",
        ['adultemotedisabled'] = "Бонк! Эмоции взрослых отлючены 🔞",
        ['toggle_instructions'] = "Переключение подсказок",
        ['exit_binoculars'] = "Выйти из бинокля",
        ['toggle_binoculars_vision'] = "Переключение между режимами бинокля",
        ['exit_news'] = "Выход из камеры новостей",
        ['toggle_news_vision'] = "Переключение между режимами камеры",
        ['edit_values_newscam'] = "Редактировать текст новости",
        ['not_in_a_vehicle'] = "Вы не можете проигрывать эту эмоцию в машине",
        ['in_a_vehicle'] = "Вы можете проигрывать эту эмоцию в машине",
        ['no_anim_crawling'] = "Вы не можете воиспроизвести анимацию во время ползания",
        ['no_anim_right_now'] = "Вы не можете воспроизвести анимацию прямо сейчас",
        -- Key maps
        ['register_cancel_emote'] = "Отменить текущую эмоцию",
        ['register_open_menu'] = "Открыть меню анимации",
        ['register_fav_anim'] = "Воспроизведите свою любимую эмоцию",
        ['register_handsup'] = "Поднимите руки вверх",
        ['register_crouch'] = "В приседи",
        ['register_crawl'] = "Ползти",
        ['register_pointing'] = "Указать пальцем",
        ['register_ragdoll'] = "Переключить рэгдолл",
        -- Commands descriptions
        ['cancel_emote'] = "Отменить текущую эмоцию",
        ['crouch'] = "В приседи",
        ['crawl'] = "Ползти",
        ['pointing'] = "Указывать пальцем"    
}
