-- Emotes you add in the file will automatically be added to AnimationList.lua
-- If you have multiple custom list files they MUST be added between AnimationList.lua and Emote.lua in fxmanifest.lua!
-- Don't change 'CustomDP' it is local to this file!

-- Remove the } from the = {} then enter your own animation code ---
-- Don't forget to close the tables.

local CustomDP = {}

CustomDP.Expressions = {}
CustomDP.Walks = {}
CustomDP.Shared = {
    ["pcarrya1"] = {
        "pcarrya1@animations",
        "pcarrya1clip",
        "PCarry 1 Shoulder Covering Eyes",
        "pcarrya2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarrya2"] = {
        "pcarrya2@animations",
        "pcarrya2clip",
        "PCarry 2 Shoulder Covering Eyes",
        "pcarrya1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = 0.000,
            yPos = 0.000,
            zPos = 0.000,
            xRot = 0.000,
            yRot = 0.000,
            zRot = 0.000
        }
    },
    ["pcarryb1"] = {
        "pcarryb1@animations",
        "pcarryb1clip",
        "PCarry 1 Struggle Behind",
        "pcarryb2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryb2"] = {
        "pcarryb2@animations",
        "pcarryb2clip",
        "PCarry 2 Struggle Behind",
        "pcarryb1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = 0.000,
            yPos = 0.000,
            zPos = 0.000,
            xRot = 0.000,
            yRot = 0.000,
            zRot = 0.000
        }
    },
    ["pcarryc1"] = {
        "pcarryc1@animations",
        "pcarryc1clip",
        "PCarry 1 Dead Behind",
        "pcarryc2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryc2"] = {
        "pcarryc2@animations",
        "pcarryc2clip",
        "PCarry 2 Dead Behind",
        "pcarryc1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = -0.360,
            yPos = 0.000,
            zPos = -0.020,
            xRot = 0.0,
            yRot = 0.0,
            zRot = -10.0
        }
    },
    ["pcarryd1"] = {
        "pcarryd1@animations",
        "pcarryd1clip",
        "PCarry 1 Firemans Shoulder",
        "pcarryd2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryd2"] = {
        "pcarryd2@animations",
        "pcarryd2clip",
        "PCarry 2 Firemans Shoulder",
        "pcarryd1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = -0.120,
            yPos = -0.150,
            zPos = -0.050,
            xRot = 0.000,
            yRot = 0.000,
            zRot = -3.000
        }
    },
    ["pcarrye1"] = {
        "pcarrye1@animations",
        "pcarrye1clip",
        "PCarry 1 Standing Shoulder",
        "pcarrye2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarrye2"] = {
        "pcarrye2@animations",
        "pcarrye2clip",
        "PCarry 2 Standing Shoulder",
        "pcarrye1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = 1.230,
            yPos = 0.020,
            zPos = 0.000,
            xRot = 0.000,
            yRot = 0.000,
            zRot = 0.000
        }
    },
    ["pcarryf1"] = {
        "pcarryf1@animations",
        "pcarryf1clip",
        "PCarry 1 Meditation Feet On Head",
        "pcarryf2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryf2"] = {
        "pcarryf2@animations",
        "pcarryf2clip",
        "PCarry 2 Meditation Feet On Head",
        "pcarryf1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = 1.230,
            yPos = 0.020,
            zPos = 0.000,
            xRot = 0.000,
            yRot = 0.000,
            zRot = 0.000
        }
    },
    ["pcarryg1"] = {
        "pcarryg1@animations",
        "pcarryg1clip",
        "PCarry 1 Superman",
        "pcarryg2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryg2"] = {
        "pcarryg2@animations",
        "pcarryg2clip",
        "PCarry 2 Superman",
        "pcarryg1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = 0.850,
            yPos = -0.120,
            zPos = -0.020,
            xRot = 0.000,
            yRot = 0.000,
            zRot = 0.000
        }
    },
    ["pcarryh1"] = {
        "pcarryh1@animations",
        "pcarryh1clip",
        "PCarry 1 Cute Shoulder Back",
        "pcarryh2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryh2"] = {
        "pcarryh2@animations",
        "pcarryh2clip",
        "PCarry 2 Cute Shoulder Back",
        "pcarryh1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = -0.130,
            yPos = -0.130,
            zPos = -0.050,
            xRot = 0.000,
            yRot = 0.000,
            zRot = 0.000
        }
    },
    ["pcarryi1"] = {
        "pcarryi1@animations",
        "pcarryi1clip",
        "PCarry 1 Bird",
        "pcarryi2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryi2"] = {
        "pcarryi2@animations",
        "pcarryi2clip",
        "PCarry 2 Bird",
        "pcarryi1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = 0.700,
            yPos = -0.330,
            zPos = 0.000,
            xRot = 0.000,
            yRot = 0.000,
            zRot = -15.000
        }
    },
    ["pcarryj1"] = {
        "pcarryj1@animations",
        "pcarryj1clip",
        "PCarry 1 Woohoo",
        "pcarryj2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryj2"] = {
        "pcarryj2@animations",
        "pcarryj2clip",
        "PCarry 2 Woohoo",
        "pcarryj1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = 0.350,
            yPos = -0.490,
            zPos = 0.000,
            xRot = 0.000,
            yRot = 0.000,
            zRot = -30.000
        }
    },
    ["pcarryk1"] = {
        "pcarryk1@animations",
        "pcarryk1clip",
        "PCarry 1 Sad Curl Up",
        "pcarryk2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryk2"] = {
        "pcarryk2@animations",
        "pcarryk2clip",
        "PCarry 2 Sad Curl Up",
        "pcarryk1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = 0.060,
            yPos = 0.100,
            zPos = 0.000,
            xRot = 0.000,
            yRot = 0.000,
            zRot = 0.000
        }
    },
    ["pcarryl1"] = {
        "pcarryl1@animations",
        "pcarryl1clip",
        "PCarry 1 Sad Front",
        "pcarryl2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryl2"] = {
        "pcarryl2@animations",
        "pcarryl2clip",
        "PCarry 2 Sad Front",
        "pcarryl1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = 0.050,
            yPos = 0.070,
            zPos = 0.020,
            xRot = 0.000,
            yRot = 0.000,
            zRot = 0.000
        }
    },
    ["pcarrym1"] = {
        "pcarrym1@animations",
        "pcarrym1clip",
        "PCarry 1 Standing Upside Down",
        "pcarrym2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarrym2"] = {
        "pcarrym2@animations",
        "pcarrym2clip",
        "PCarry 2 Standing Upside Down",
        "pcarrym1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = 1.820,
            yPos = 0.150,
            zPos = -0.020,
            xRot = 0.000,
            yRot = 0.000,
            zRot = 5.000
        }
    },
    ["pcarryn1"] = {
        "pcarryn1@animations",
        "pcarryn1clip",
        "PCarry 1 Salute",
        "pcarryn2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryn2"] = {
        "pcarryn2@animations",
        "pcarryn2clip",
        "PCarry 2 Salute",
        "pcarryn1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = 1.230,
            yPos = 0.020,
            zPos = 0.000,
            xRot = 0.000,
            yRot = 0.000,
            zRot = 0.000
        }
    },
    ["pcarryo1"] = {
        "pcarryo1@animations",
        "pcarryo1clip",
        "PCarry 1 Arrogant",
        "pcarryo2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryo2"] = {
        "pcarryo2@animations",
        "pcarryo2clip",
        "PCarry 2 Arrogant",
        "pcarryo1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = 0.550,
            yPos = 0.010,
            zPos = 0.000,
            xRot = 0.000,
            yRot = 0.000,
            zRot = 0.000
        }
    },
    ["pcarryp1"] = {
        "pcarryp1@animations",
        "pcarryp1clip",
        "PCarry 1 Dab",
        "pcarryp2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryp2"] = {
        "pcarryp2@animations",
        "pcarryp2clip",
        "PCarry 2 Dab",
        "pcarryp1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = 0.550,
            yPos = 0.010,
            zPos = 0.000,
            xRot = 0.000,
            yRot = 0.000,
            zRot = 0.000
        }
    },
    ["pcarryq1"] = {
        "pcarryq1@animations",
        "pcarryq1clip",
        "PCarry 1 Bull Meditation",
        "pcarryq2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryq2"] = {
        "pcarryq2@animations",
        "pcarryq2clip",
        "PCarry 2 Bull Meditation",
        "pcarryq1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = -0.200,
            yPos = -0.500,
            zPos = 0.000,
            xRot = 0.000,
            yRot = 0.000,
            zRot = -80.000
        }
    },
    ["pcarryr1"] = {
        "pcarryr1@animations",
        "pcarryr1clip",
        "PCarry 1 Cute Shy Shoulder",
        "pcarryr2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryr2"] = {
        "pcarryr2@animations",
        "pcarryr2clip",
        "PCarry 2 Cute Shy Shoulder",
        "pcarryr1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = -0.070,
            yPos = -0.100,
            zPos = -0.280,
            xRot = 0.000,
            yRot = 0.000,
            zRot = 0.000
        }
    },
    ["pcarrys1"] = {
        "pcarrys1@animations",
        "pcarrys1clip",
        "PCarry 2 Sleep Over Head",
        "pcarrys2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarrys2"] = {
        "pcarrys2@animations",
        "pcarrys2clip",
        "PCarry 2 Sleep Over Head",
        "pcarrys1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = 0.880,
            yPos = 0.000,
            zPos = -0.080,
            xRot = 0.000,
            yRot = 0.000,
            zRot = 0.000
        }
    },
    ["pcarryt1"] = {
        "pcarryt1@animations",
        "pcarryt1clip",
        "PCarry 1 Riding Motorcycle",
        "pcarryt2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryt2"] = {
        "pcarryt2@animations",
        "pcarryt2clip",
        "PCarry 2 Riding Motorcycle",
        "pcarryt1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = 0.520,
            yPos = 0.030,
            zPos = -0.010,
            xRot = 0.000,
            yRot = 0.000,
            zRot = 0.000
        }
    },
    ["pcarryu1"] = {
        "pcarryu1@animations",
        "pcarryu1clip",
        "PCarry 1 Cute Sit Shoulder",
        "pcarryu2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryu2"] = {
        "pcarryu2@animations",
        "pcarryu2clip",
        "PCarry 2 Cute Sit Shoulder",
        "pcarryu1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = 0.290,
            yPos = 0.040,
            zPos = -0.220,
            xRot = 0.000,
            yRot = 0.000,
            zRot = 0.000
        }
    },
    ["pcarryv1"] = {
        "pcarryv1@animations",
        "pcarryv1clip",
        "PCarry 1 Pull Head Back",
        "pcarryv2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryv2"] = {
        "pcarryv2@animations",
        "pcarryv2clip",
        "PCarry 2 Pull Head Back",
        "pcarryv1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = -0.340,
            yPos = -0.180,
            zPos = 0.150,
            xRot = 0.000,
            yRot = 0.000,
            zRot = 50.000
        }
    },
    ["pcarryw1"] = {
        "pcarryw1@animations",
        "pcarryw1clip",
        "PCarry 1 Disgusting",
        "pcarryw2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryw2"] = {
        "pcarryw2@animations",
        "pcarryw2clip",
        "PCarry 2 Disgusting",
        "pcarryw1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 28422,
            xPos = 0.000,
            yPos = -0.140,
            zPos = -0.410,
            xRot = 0.000,
            yRot = 0.000,
            zRot = 0.000
        }
    },
    ["pcarryx1"] = {
        "pcarryx1@animations",
        "pcarryx1clip",
        "PCarry 1 Caught",
        "pcarryx2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryx2"] = {
        "pcarryx2@animations",
        "pcarryx2clip",
        "PCarry 2 Caught",
        "pcarryx1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 28422,
            xPos = 0.250,
            yPos = -0.200,
            zPos = 0.130,
            xRot = 0.000,
            yRot = 0.000,
            zRot = 0.000
        }
    },
    ["pcarryy1"] = {
        "pcarryy1@animations",
        "pcarryy1clip",
        "PCarry 1 Torture",
        "pcarryy2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryy2"] = {
        "pcarryy2@animations",
        "pcarryy2clip",
        "PCarry 2 Torture",
        "pcarryy1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 28422,
            xPos = 0.240,
            yPos = -0.560,
            zPos = 0.750,
            xRot = 0.000,
            yRot = 0.000,
            zRot = -160.000
        }
    },
    ["pcarryz1"] = {
        "pcarryz1@animations",
        "pcarryz1clip",
        "PCarry 1 Bazoka",
        "pcarryz2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryz2"] = {
        "pcarryz2@animations",
        "pcarryz2clip",
        "PCarry 2 Bazoka",
        "pcarryz1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 57005,
            xPos = 0.020,
            yPos = 0.190,
            zPos = -0.220,
            xRot = 0.0,
            yRot = 0.0,
            zRot = -79.999
        }
    },
    ["pcarryza1"] = {
        "pcarryza1@animations",
        "pcarryza1clip",
        "PCarry 1 Drunk Behind",
        "pcarryza2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryza2"] = {
        "pcarryza2@animations",
        "pcarryza2clip",
        "PCarry 2 Drunk Behind",
        "pcarryza1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = -0.560,
            yPos = 0.030,
            zPos = 0.000,
            xRot = 0.000,
            yRot = 0.000,
            zRot = 0.000
        }
    },
    ["pcarryzb1"] = {
        "pcarryzb1@animations",
        "pcarryzb1clip",
        "PCarry 1 Peek High",
        "pcarryzb2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryzb2"] = {
        "pcarryzb2@animations",
        "pcarryzb2clip",
        "PCarry 2 Peek High",
        "pcarryzb1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = 1.943,
            yPos = -0.010,
            zPos = -0.024,
            xRot = 0.000,
            yRot = 0.000,
            zRot = 0.000
        }
    },
    ["pcarryzc1"] = {
        "pcarryzc1@animations",
        "pcarryzc1clip",
        "PCarry 1 Meditation Master",
        "pcarryzc2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryzc2"] = {
        "pcarryzc2@animations",
        "pcarryzc2clip",
        "PCarry 2 Meditation Master",
        "pcarryzc1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = 1.160,
            yPos = 0.020,
            zPos = -0.020,
            xRot = 0.000,
            yRot = 0.000,
            zRot = 0.000
        }
    },
    ["pcarryzd1"] = {
        "pcarryzd1@animations",
        "pcarryzd1clip",
        "PCarry 1 Strongman",
        "pcarryzd2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryzd2"] = {
        "pcarryzd2@animations",
        "pcarryzd2clip",
        "PCarry 2 Strongman",
        "pcarryzd1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = 0.590,
            yPos = 0.010,
            zPos = -0.020,
            xRot = 0.000,
            yRot = 0.000,
            zRot = 0.000
        }
    },
    ["pcarryze1"] = {
        "pcarryze1@animations",
        "pcarryze1clip",
        "PCarry 1 Cute Piggyback",
        "pcarryze2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryze2"] = {
        "pcarryze2@animations",
        "pcarryze2clip",
        "PCarry 2 Cute Piggyback",
        "pcarryze1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = 0.180,
            yPos = -0.020,
            zPos = -0.020,
            xRot = 0.000,
            yRot = 0.000,
            zRot = -10.000
        }
    },
    ["pcarryzf1"] = {
        "pcarryzf1@animations",
        "pcarryzf1clip",
        "PCarry 1 Piggyback Run",
        "pcarryzf2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryzf2"] = {
        "pcarryzf2@animations",
        "pcarryzf2clip",
        "PCarry 2 Piggyback Run",
        "pcarryzf1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = -0.210,
            yPos = -0.270,
            zPos = -0.020,
            xRot = 0.000,
            yRot = 0.000,
            zRot = -90.000
        }
    },
    ["pcarryzg1"] = {
        "pcarryzg1@animations",
        "pcarryzg1clip",
        "PCarry 1 Pulling Hands Back",
        "pcarryzg2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryzg2"] = {
        "pcarryzg2@animations",
        "pcarryzg2clip",
        "PCarry 2 Pulling Hands Back",
        "pcarryzg1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = -0.150,
            yPos = -0.670,
            zPos = -0.010,
            xRot = 0.000,
            yRot = 0.000,
            zRot = -80.000
        }
    },
    ["pcarryzh1"] = {
        "pcarryzh1@animations",
        "pcarryzh1clip",
        "PCarry 1 Pole",
        "pcarryzh2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryzh2"] = {
        "pcarryzh2@animations",
        "pcarryzh2clip",
        "PCarry 2 Pole",
        "pcarryzh1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = -0.110,
            yPos = 0.120,
            zPos = -0.270,
            xRot = 0.000,
            yRot = 0.000,
            zRot = -10.000
        }
    },
    ["pcarryzi1"] = {
        "pcarryzi1@animations",
        "pcarryzi1clip",
        "PCarry 1 Cute Front",
        "pcarryzi2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryzi2"] = {
        "pcarryzi2@animations",
        "pcarryzi2clip",
        "PCarry 2 Cute Front",
        "pcarryzi1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = 0.100,
            yPos = 0.250,
            zPos = 0.000,
            xRot = 0.000,
            yRot = 0.000,
            zRot = 0.000
        }
    },
    ["pcarryzj1"] = {
        "pcarryzj1@animations",
        "pcarryzj1clip",
        "PCarry 1 Caress Head",
        "pcarryzj2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryzj2"] = {
        "pcarryzj2@animations",
        "pcarryzj2clip",
        "PCarry 2 Caress Head",
        "pcarryzj1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = 0.270,
            yPos = 0.010,
            zPos = -0.060,
            xRot = 0.000,
            yRot = 0.000,
            zRot = 0.000
        }
    },
    ["pcarryzk1"] = {
        "pcarryzk1@animations",
        "pcarryzk1clip",
        "PCarry 1 Cute Scared",
        "pcarryzk2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryzk2"] = {
        "pcarryzk2@animations",
        "pcarryzk2clip",
        "PCarry 2 Cute Scared",
        "pcarryzk1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = -0.200,
            yPos = 0.010,
            zPos = 0.100,
            xRot = 0.000,
            yRot = 0.000,
            zRot = 0.000
        }
    },
    ["pcarryzl1"] = {
        "pcarryzl1@animations",
        "pcarryzl1clip",
        "PCarry 1 Bag",
        "pcarryzl2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryzl2"] = {
        "pcarryzl2@animations",
        "pcarryzl2clip",
        "PCarry 2 Bag",
        "pcarryzl1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = 0.040,
            yPos = 0.060,
            zPos = -0.030,
            xRot = 0.000,
            yRot = 0.000,
            zRot = 0.000
        }
    },
    ["pcarryzm1"] = {
        "pcarryzm1@animations",
        "pcarryzm1clip",
        "PCarry 1 Bag Flip",
        "pcarryzm2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryzm2"] = {
        "pcarryzm2@animations",
        "pcarryzm2clip",
        "PCarry 2 Bag Flip",
        "pcarryzm1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = -0.120,
            yPos = 0.050,
            zPos = -0.020,
            xRot = 0.000,
            yRot = 0.000,
            zRot = -15.000
        }
    },
    ["pcarryzn1"] = {
        "pcarryzn1@animations",
        "pcarryzn1clip",
        "PCarry 1 Hug Back Flip",
        "pcarryzn2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryzn2"] = {
        "pcarryzn2@animations",
        "pcarryzn2clip",
        "PCarry 2 Hug Back Flip",
        "pcarryzn1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = -0.210,
            yPos = 0.050,
            zPos = -0.020,
            xRot = 0.000,
            yRot = 0.000,
            zRot = -15.000
        }
    },
    ["pcarryzo1"] = {
        "pcarryzo1@animations",
        "pcarryzo1clip",
        "PCarry 1 Sit & Wave Over Head",
        "pcarryzo2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryzo2"] = {
        "pcarryzo2@animations",
        "pcarryzo2clip",
        "PCarry 2 Sit & Wave Over Head",
        "pcarryzo1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = 1.190,
            yPos = -0.020,
            zPos = -0.020,
            xRot = 0.000,
            yRot = 0.000,
            zRot = 0.000
        }
    },
    ["pcarryzp1"] = {
        "pcarryzp1@animations",
        "pcarryzp1clip",
        "PCarry 1 Cute Punch Head",
        "pcarryzp2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryzp2"] = {
        "pcarryzp2@animations",
        "pcarryzp2clip",
        "PCarry 2 Cute Punch Head",
        "pcarryzp1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = 0.450,
            yPos = -0.160,
            zPos = -0.030,
            xRot = 0.000,
            yRot = 0.000,
            zRot = 0.000
        }
    },
    ["pcarryzq1"] = {
        "pcarryzq1@animations",
        "pcarryzq1clip",
        "PCarry 1 Superhero",
        "pcarryzq2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryzq2"] = {
        "pcarryzq2@animations",
        "pcarryzq2clip",
        "PCarry 2 Superhero",
        "pcarryzq1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = 0.270,
            yPos = 0.540,
            zPos = -0.020,
            xRot = 0.000,
            yRot = 0.000,
            zRot = 0.000
        }
    },
    ["pcarryzr1"] = {
        "pcarryzr1@animations",
        "pcarryzr1clip",
        "PCarry 1 Shoulder Leg Cross Head",
        "pcarryzr2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryzr2"] = {
        "pcarryzr2@animations",
        "pcarryzr2clip",
        "PCarry 2 Shoulder Leg Cross Head",
        "pcarryzr1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = 0.600,
            yPos = -0.090,
            zPos = 0.020,
            xRot = 0.000,
            yRot = 0.000,
            zRot = 0.000
        }
    },
    ["pcarryzs1"] = {
        "pcarryzs1@animations",
        "pcarryzs1clip",
        "PCarry 1 Can't Breath",
        "pcarryzs2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryzs2"] = {
        "pcarryzs2@animations",
        "pcarryzs2clip",
        "PCarry 2 Can't Breath",
        "pcarryzs1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = -0.030,
            yPos = -0.180,
            zPos = 0.020,
            xRot = 0.000,
            yRot = 0.000,
            zRot = 0.000
        }
    },
    ["pcarryzt1"] = {
        "pcarryzt1@animations",
        "pcarryzt1clip",
        "PCarry 1 Firemans Back",
        "pcarryzt2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryzt2"] = {
        "pcarryzt2@animations",
        "pcarryzt2clip",
        "PCarry 2 Firemans Back",
        "pcarryzt1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = -0.330,
            yPos = 0.000,
            zPos = -0.040,
            xRot = 0.000,
            yRot = 0.000,
            zRot = -80.000
        }
    },
    ["pcarryzu1"] = {
        "pcarryzu1@animations",
        "pcarryzu1clip",
        "PCarry 1 Happy Behind",
        "pcarryzu2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryzu2"] = {
        "pcarryzu2@animations",
        "pcarryzu2clip",
        "PCarry 2 Happy Behind",
        "pcarryzu1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = -0.290,
            yPos = -0.300,
            zPos = -0.020,
            xRot = 0.000,
            yRot = 0.000,
            zRot = -80.000
        }
    },
    ["pcarryzv1"] = {
        "pcarryzv1@animations",
        "pcarryzv1clip",
        "PCarry 1 Happy Swing",
        "pcarryzv2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryzv2"] = {
        "pcarryzv2@animations",
        "pcarryzv2clip",
        "PCarry 2 Happy Swing",
        "pcarryzv1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = -0.040,
            yPos = -0.720,
            zPos = -0.600,
            xRot = 0.000,
            yRot = 0.000,
            zRot = 0.000
        }
    },
    ["pcarryzw1"] = {
        "pcarryzw1@animations",
        "pcarryzw1clip",
        "PCarry 1 Piggyback Head",
        "pcarryzw2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryzw2"] = {
        "pcarryzw2@animations",
        "pcarryzw2clip",
        "PCarry 2 Piggyback Head",
        "pcarryzw1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = 0.320,
            yPos = -0.220,
            zPos = -0.020,
            xRot = 0.000,
            yRot = 0.000,
            zRot = -80.000
        }
    },
    ["pcarryzx1"] = {
        "pcarryzx1@animations",
        "pcarryzx1clip",
        "PCarry 1 Pose Over Head",
        "pcarryzx2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryzx2"] = {
        "pcarryzx2@animations",
        "pcarryzx2clip",
        "PCarry 2 Pose Over Head",
        "pcarryzx1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = 0.430,
            yPos = -0.860,
            zPos = 0.000,
            xRot = 0.000,
            yRot = 0.000,
            zRot = -80.000
        }
    },
    ["pcarryzy1"] = {
        "pcarryzy1@animations",
        "pcarryzy1clip",
        "PCarry 1 Piggyback Hold Head",
        "pcarryzy2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryzy2"] = {
        "pcarryzy2@animations",
        "pcarryzy2clip",
        "PCarry 2 Piggyback Hold Head",
        "pcarryzy1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = 0.060,
            yPos = -0.070,
            zPos = -0.020,
            xRot = 0.000,
            yRot = 0.000,
            zRot = -20.000
        }
    },
    ["pcarryzz1"] = {
        "pcarryzz1@animations",
        "pcarryzz1clip",
        "PCarry 1 Heart Power",
        "pcarryzz2",
        AnimationOptions = {
            EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["pcarryzz2"] = {
        "pcarryzz2@animations",
        "pcarryzz2clip",
        "PCarry 2 Heart Power",
        "pcarryzz1",
        AnimationOptions = {
            EmoteMoving = false,
            EmoteLoop = true,
            Attachto = true,
            bone = 24818,
            xPos = 1.110,
            yPos = 0.210,
            zPos = -0.780,
            xRot = 0.000,
            yRot = 0.000,
            zRot = 0.000
        }
    },
}
CustomDP.Dances = {
    ["zdance1"] = {
        "zdance1@animations",
        "zdance1_clip",
        "ZDance 1",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["zdance2"] = {
        "zdance2@animations",
        "zdance2_clip",
        "ZDance 2",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["zdance3"] = {
        "zdance3@animations",
        "zdance3_clip",
        "ZDance 3",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["zdance4"] = {
        "zdance4@animations",
        "zdance4_clip",
        "ZDance 4",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["zdance5"] = {
        "zdance5@animations",
        "zdance5_clip",
        "ZDance 5",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["zdance6"] = {
        "zdance6@animations",
        "zdance6_clip",
        "ZDance 6",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["zdance7"] = {
        "zdance7@animations",
        "zdance7_clip",
        "ZDance 7",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["zdance8"] = {
        "zdance8@animations",
        "zdance8_clip",
        "ZDance 8",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["zdance9"] = {
        "zdance9@animations",
        "zdance9_clip",
        "ZDance 9",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["zdance10"] = {
        "zdance10@animations",
        "zdance10_clip",
        "ZDance 10",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["zdance11"] = {
        "zdance11@animations",
        "zdance11_clip",
        "ZDance 11",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["zdance12"] = {
        "zdance12@animations",
        "zdance12_clip",
        "ZDance 12",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["zdance13"] = {
        "zdance13@animations",
        "zdance13_clip",
        "ZDance 13",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["zdance14"] = {
        "zdance14@animations",
        "zdance14_clip",
        "ZDance 14",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["zdance15"] = {
        "zdance15@animations",
        "zdance15_clip",
        "ZDance 15",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["zdance16"] = {
        "zdance16@animations",
        "zdance16_clip",
        "ZDance 16",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["zdance17"] = {
        "zdance17@animations",
        "zdance17_clip",
        "ZDance 17",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["zdance18"] = {
        "zdance18@animations",
        "zdance18_clip",
        "ZDance 18",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["zdance19"] = {
        "zdance19@animations",
        "zdance19_clip",
        "ZDance 19",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["zdance20"] = {
        "zdance20@animations",
        "zdance20_clip",
        "ZDance 20",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["zdance21"] = {
        "zdance21@animations",
        "zdance21_clip",
        "ZDance 21",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["zdance22"] = {
        "zdance22@animations",
        "zdance22_clip",
        "ZDance 22",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["waitdance"] = {
        "waitdance@animations",
        "waitdanceclip",
        "Wait Dance Trend",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["terminatordance"] = {
        "terminatordance@animations",
        "terminatordanceclip",
        "Terminator Dance",
        AnimationOptions = {
            EmoteLoop = true,
        }
    },
    ["segadance"] = {
        "segadance@animations",
        "segadance_clip",
        "Sega Dance",
        AnimationOptions = {
            EmoteLoop = true,
        }
    },
    ["robloxardance"] = {
        "robloxardance@animations",
        "robloxardanceclip",
        "Roblox Ar Dance",
        AnimationOptions = {
            EmoteLoop = true,
        }
    },
    ["pubghaidilaodance"] = {
        "pubghaidilaodance@animations",
        "pubghaidilaodance_clip",
        "PUBG Haidilao Dance",
        AnimationOptions = {
            EmoteLoop = true,
        }
    },
    ["danceitsmylife"] = {
        "danceitsmylife@animations",
        "danceitsmylife_clip",
        "PUBG It's My Life Dance",
        AnimationOptions = {
            EmoteLoop = true,
        }
    },
    ["pubgnastygirl"] = {
        "nastygirl@animations",
        "nastygirlclip",
        "PUBG Nasty Girl",
        AnimationOptions = {
            EmoteLoop = true,
        }
    },
    ["ptrompetraka"] = {
        "ptrompetraka@animations",
        "ptrompetrakaclip",
        "PUBG 146 Trompet Raka Dance",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pkdapopstars"] = {
        "pkdapopstars@animations",
        "pkdapopstarsclip",
        "KDA Pop Stars Dance",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["psadbor1"] = {
        "psadbor1@animations",
        "psadbor1clip",
        "Joget Sadbor",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["psadbor2"] = {
        "psadbor2@animations",
        "psadbor2clip",
        "Joget Sadbor Awal",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfriches"] = {
        "pfriches@animations",
        "pfrichesclip",
        "Fortnite Riches",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfdesirable"] = {
        "pfdesirable@animations",
        "pfdesirableclip",
        "Fortnite Desirable",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pftakeitslow"] = {
        "pftakeitslow@animations",
        "pftakeitslowclip",
        "Fortnite Take It Slow",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfthedog"] = {
        "pfthedog@animations",
        "pfthedogclip",
        "Fortnite The Dog",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfsnoopswalk"] = {
        "pfsnoopswalk@animations",
        "pfsnoopswalkclip",
        "Fortnite Snoop's Walk",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfrhythmofchaos"] = {
        "pfrhythmofchaos@animations",
        "pfrhythmofchaosclip",
        "Fortnite Rhythm of Chaos",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfmoongazer"] = {
        "pfmoongazer@animations",
        "pfmoongazerclip",
        "Fortnite Moongazer",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfcaffeinated"] = {
        "pfcaffeinated@animations",
        "pfcaffeinatedclip",
        "Fortnite Caffeinated",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfcaffeinatedold"] = {
        "pfcaffeinatedold@animations",
        "pfcaffeinatedoldclip",
        "Fortnite Caffeinated Old",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfcommitted"] = {
        "pfcommitted@animations",
        "pfcommittedclip",
        "Fortnite Committed",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfdimensional"] = {
        "pfdimensional@animations",
        "pfdimensionalclip",
        "Fortnite Dimensional",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfflytotokyo"] = {
        "pfflytotokyo@animations",
        "pfflytotokyoclip",
        "Fortnite Fly To Tokyo",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfattraction"] = {
        "pfattraction@animations",
        "pfattractionclip",
        "Fortnite Attraction",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfluciddreams"] = {
        "pfluciddreams@animations",
        "pfluciddreamsclip",
        "Fortnite Lucid Dreams",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfskeledance"] = {
        "pfskeledance@animations",
        "pfskeledanceclip",
        "Fortnite Skele Dance",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pftheviper"] = {
        "pftheviper@animations",
        "pftheviperclip",
        "Fortnite The Viper",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfgethot"] = {
        "pfgethot@animations",
        "pfgethotclip",
        "Fortnite Get Hot",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfemptyoutyourpockets"] = {
        "pfemptyoutyourpockets@animations",
        "pfemptyoutyourpocketsclip",
        "Fortnite Empty Out Your Pockets",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfrapmonster"] = {
        "pfrapmonster@animations",
        "pfrapmonsterclip",
        "Fortnite Rap Monster",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfmaskoff"] = {
        "pfmaskoff@animations",
        "pfmaskoffclip",
        "Fortnite Mask Off",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfnuthinbutagthang"] = {
        "pfnuthinbutagthang@animations",
        "pfnuthinbutagthangclip",
        "Fortnite Nuthin' But A G Thang",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfcoffin"] = {
        "pfcoffin@animations",
        "pfcoffinclip",
        "Fortnite Coffin",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfcoffinmove"] = {
        "pfcoffinmove@animations",
        "pfcoffinmoveclip",
        "Fortnite Coffin Move",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfcalifornialove"] = {
        "pfcalifornialove@animations",
        "pfcalifornialoveclip",
        "Fortnite California Love",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfbyebyebye"] = {
        "pfbyebyebye@animations",
        "pfbyebyebyeclip",
        "Fortnite Bye Bye Bye",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfsoarabove"] = {
        "pfsoarabove@animations",
        "pfsoaraboveclip",
        "Fortnite Soar Above",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfalliwantforchristmas"] = {
        "pfalliwantforchristmas@animations",
        "pfalliwantforchristmasclip",
        "Fortnite All I Want For Christmas",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["ptrump"] = {
        "ptrump@animations",
        "ptrumpclip",
        "Trump Dance",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["ptrumpsup"] = {
        "ptrumpsup@animations",
        "ptrumpsupclip",
        "Trump Supporters Dance",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["jktdance"] = {
        "jktdance@animations",
        "jktdance_clip",
        "JKT48 Hisatsu Teleport Dance",
        AnimationOptions = {
            EmoteLoop = true,
        }
    },
    ["jktdance2"] = {
        "jktdance2@animations",
        "jktdance2_clip",
        "JKT48 Heavy Rotation Dance",
        AnimationOptions = {
            EmoteLoop = true,
        }
    },
    ["jktdance3"] = {
        "jktdance3@animations",
        "jktdance3_clip",
        "JKT48 Fortune Cookie Dance",
        AnimationOptions = {
            EmoteLoop = true,
        }
    },
    ["dancepaham"] = {
        "dancepaham@animations",
        "dancepaham_clip",
        "Kak Gem Paham",
        AnimationOptions = {
            EmoteLoop = true,
        }
    },
    ["danceshikanoko"] = {
        "danceshikanoko@animations",
        "danceshikanoko_clip",
        "Shikanoko Dance",
        AnimationOptions = {
            EmoteLoop = true,
        }
    },
    ["njditto"] = {
        "njditto@animations",
        "njdittoclip",
        "New Jeans Ditto Dance",
        AnimationOptions = {
            EmoteLoop = true,
        }
    },
    ["letmeseedance"] = {
        "letmeseedance@animations",
        "letmeseedanceclip",
        "NBA 2K25 Let Me See Dance",
        AnimationOptions = {
            EmoteLoop = true,
        }
    },
    ["lolidanceslow"] = {
        "lolidanceslow@animations",
        "lolidanceslow",
        "Loli Dance Slow",
        AnimationOptions = {
            EmoteLoop = true,
        }
    },
    ["lolidancefast"] = {
        "lolidancefast@animations",
        "lolidancefast",
        "Loli Dance Fast",
        AnimationOptions = {
            EmoteLoop = true,
        }
    },
    ["thickofit"] = {
        "thickofitdance@animations",
        "thickofitdanceclip",
        "Thick Of It 1950s Dance",
        AnimationOptions = {
            EmoteLoop = true,
        }
    },
    ["ishowspeeddance"] = {
        "ishowspeedcrispeyspraydance@animations",
        "ishowspeedcrispeyspraydanceclip",
        "IShowSpeed Dance",
        AnimationOptions = {
            EmoteLoop = true,
        }
    },
    ["ifyoudance1"] = {
        "ifyoudancep1@animations",
        "ifyoudancep1_clip",
        "If You Dance Player 1",
        AnimationOptions = {
            EmoteLoop = true,
        }
    },
    ["ifyoudance2"] = {
        "ifyoudancep2@animations",
        "ifyoudancep2_clip",
        "If You Dance Player 2",
        AnimationOptions = {
            EmoteLoop = true,
        }
    },
    ["plingaguliguli"] = {
        "plingaguliguli@animations",
        "plingaguliguliclip",
        "Linga Guli Guli Dance Doodle",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["plumbairibilangbos"] = {
        "plumbairibilangbos@animations",
        "plumbairibilangbosclip",
        "Goyang Lumba Iri Bilang Bos",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["plumbalumbajoget"] = {
        "plumbalumbajoget@animations",
        "plumbalumbajogetclip",
        "Goyang Lumba Lumba Joget",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["ppokedance"] = {
        "ppokedance@animations",
        "ppokedanceclip",
        "Poke Dance",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pkawaikutegomen"] = {
        "pkawaikutegomen@animations",
        "pkawaikutegomenclip",
        "Kawaikute Gomen Dance",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["chipichapa"] = {
        "chipichapa@animations",
        "chipichapaclip",
        "Chipi Chipi Chapa Chapa Dance",
        AnimationOptions = {
            EmoteLoop = true,
        }
    },
    ["pfmikulive"] = {
        "pfmikulive@animations",
        "pfmikuliveclip",
        "Fortnite Miku Live",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pffeelit"] = {
        "pffeelit@animations",
        "pffeelitclip",
        "Fortnite Feel It",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfstartingprance"] = {
        "pfstartingprance@animations",
        "pfstartingpranceclip",
        "Fortnite Starting Prance",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfskyward"] = {
        "pfskyward@animations",
        "pfskywardclip",
        "Fortnite Skyward",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfsmoothoperator"] = {
        "pfsmoothoperator@animations",
        "pfsmoothoperatorclip",
        "Fortnite Smooth Operator",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfbratty"] = {
        "pfbratty@animations",
        "pfbrattyclip",
        "Fortnite Bratty",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfinhamood"] = {
        "pfinhamood@animations",
        "pfinhamoodclip",
        "Fortnite In Ha Mood",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfspicystart"] = {
        "pfspicystart@animations",
        "pfspicystartclip",
        "Fortnite Spicy Start",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfdeepexplorer"] = {
        "pfdeepexplorer@animations",
        "pfdeepexplorerclip",
        "Fortnite Deep Explorer",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfwhatyouwant"] = {
        "pfwhatyouwant@animations",
        "pfwhatyouwantclip",
        "Fortnite What You Want",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pflinedancin"] = {
        "pflinedancin@animations",
        "pflinedancinclip",
        "Fortnite Line Dancin",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfindependence"] = {
        "pfindependence@animations",
        "pfindependenceclip",
        "Fortnite Independence",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfcairo"] = {
        "pfcairo@animations",
        "pfcairoclip",
        "Fortnite Cairo",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfokidoki"] = {
        "pfokidoki@animations",
        "pfokidokiclip",
        "Fortnite Oki Doki",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfoutlaw"] = {
        "pfoutlaw@animations",
        "pfoutlawclip",
        "Fortnite Outlaw",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfnotears"] = {
        "pfnotears@animations",
        "pfnotearsclip",
        "Fortnite No Tears",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfmine"] = {
        "pfmine@animations",
        "pfmineclip",
        "Fortnite Mine",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pflookinggood"] = {
        "pflookinggood@animations",
        "pflookinggoodclip",
        "Fortnite Looking Good",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfheelclickbreakdown"] = {
        "pfheelclickbreakdown@animations",
        "pfheelclickbreakdownclip",
        "Fortnite Heel Click Breakdown",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfentranced"] = {
        "pfentranced@animations",
        "pfentrancedclip",
        "Fortnite Entranced",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pffeelitfly"] = {
        "pffeelitfly@animations",
        "pffeelitflyclip",
        "Fortnite Feel It Fly",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["psquidgameround"] = {
        "psquidgameround@animations",
        "psquidgameroundclip",
        "Squid Game Round and Round",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pcatui"] = {
        "pcatui@animations",
        "pcatuiclip",
        "Meme Cat UI Dance",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pcatuai1"] = {
        "pcatuai1@animations",
        "pcatuai1clip",
        "Meme Cat UAI Dance",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pcatuai2"] = {
        "pcatuai2@animations",
        "pcatuai2clip",
        "Meme Cat UAI Dance Fast",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pgaramdanmadu"] = {
        "pgaramdanmadu@animations",
        "pgaramdanmaduclip",
        "Garam Dan Madu Dance",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pratdance"] = {
        "pratdance@animations",
        "pratdanceclip",
        "Rat Dance",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pmeninadojob"] = {
        "pmeninadojob@animations",
        "pmeninadojobclip",
        "PUBG Menina Do Job Dance",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["ponthefloordance"] = {
        "ponthefloordance@animations",
        "ponthefloordanceclip",
        "PUBG On The Floor Dance",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["plangit"] = {
        "pazeeetembaklangit@animations",
        "pazeeetembaklangitclip",
        "Tembak Langit Dance",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pparjamban1"] = {
        "pazeeeparjamban1@animations",
        "pazeeeparjamban1clip",
        "Parjamban Emote",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pparjamban2"] = {
        "pazeeeparjamban2@animations",
        "pazeeeparjamban2clip",
        "Parjamban Dance",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pparjamban3"] = {
        "pazeeeparjamban3@animations",
        "pazeeeparjamban3clip",
        "Parjamban Dance Motorcycle",
        AnimationOptions = {
            EmoteLoop = true,
			FullBody = true
        }
    },
    ["pparjamban4"] = {
        "pazeeeparjamban4@animations",
        "pazeeeparjamban4clip",
        "Parjamban Dance Car",
        AnimationOptions = {
            EmoteLoop = true,
			FullBody = true
        }
    },
    ["pkendrick1"] = {
        "pazeeekendrick1@animations",
        "pazeeekendrick1clip",
        "Kendrick Lamar Dance",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pkendrick2"] = {
        "pazeeekendrick2@animations",
        "pazeeekendrick2clip",
        "Kendrick Lamar Dance Move",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfstarlit"] = {
        "pazeeefortnitestarlit@animations",
        "pazeeefortnitestarlitclip",
        "Fortnite Starlit",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfboneybounce"] = {
        "pazeeefortniteboneybounce@animations",
        "pazeeefortniteboneybounceclip",
        "Fortnite Boney Bounce",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfevilplan"] = {
        "pazeeefortniteevilplan@animations",
        "pazeeefortniteevilplanclip",
        "Fortnite Evil Plan",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfdancindomino"] = {
        "pazeeefortnitedancindomino@animations",
        "pazeeefortnitedancindominoclip",
        "Fortnite Dancin' Domino",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfpointandstrut"] = {
        "pazeeefortnitepointandstrut@animations",
        "pazeeefortnitepointandstrutclip",
        "Fortnite Point And Strut",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfthedancelaroi"] = {
        "pazeeefortnitethedancelaroi@animations",
        "pazeeefortnitethedancelaroiclip",
        "Fortnite The Dance Laroi",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfcopines"] = {
        "pazeeefortnitecopines@animations",
        "pazeeefortnitecopinesclip",
        "Fortnite Copines",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfmikubeam"] = {
        "pazeeefortnitemikubeam@animations",
        "pazeeefortnitemikubeamclip",
        "Fortnite Miku Miku Beam",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfitstrue"] = {
        "pazeeefortniteitstrue@animations",
        "pazeeefortniteitstrueclip",
        "Fortnite It's True",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfimout"] = {
        "pazeeefortniteimout@animations",
        "pazeeefortniteimoutclip",
        "Fortnite I'm Out",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfscenario"] = {
        "pazeeefortnitescenario@animations",
        "pazeeefortnitescenarioclip",
        "Fortnite Scenario",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfjabbaswitchway"] = {
        "pazeeefortnitejabbaswitchway@animations",
        "pazeeefortnitejabbaswitchwayclip",
        "Fortnite Jabba Switchway",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfgomufasa"] = {
        "pazeeefortnitegomufasa@animations",
        "pazeeefortnitegomufasaclip",
        "Fortnite Go Mufasa",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfgomufasamove"] = {
        "pazeeefortnitegomufasamove@animations",
        "pazeeefortnitegomufasamoveclip",
        "Fortnite Go Mufasa Move",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfeverybodylovesme"] = {
        "pazeeefortniteeverybodylovesme@animations",
        "pazeeefortniteeverybodylovesmeclip",
        "Fortnite Everybody Loves Me",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfgetgriddy"] = {
        "pazeeefortnitegetgriddy@animations",
        "pazeeefortnitegetgriddyclip",
        "Fortnite Get Griddy",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfgetgriddymove"] = {
        "pazeeefortnitegetgriddymove@animations",
        "pazeeefortnitegetgriddymoveclip",
        "Fortnite Get Griddy Move",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pflofiheadbang"] = {
        "pazeeefortnitelofiheadbang@animations",
        "pazeeefortnitelofiheadbangclip",
        "Fortnite Lo-Fi Headbang",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfrebellious"] = {
        "pazeeefortniterebellious@animations",
        "pazeeefortniterebelliousclip",
        "Fortnite Rebellious",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfbackon74"] = {
        "pazeeefortnitebackon74@animations",
        "pazeeefortnitebackon74clip",
        "Fortnite Back On 74",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["pfbackon74move"] = {
        "pazeeefortnitebackon74move@animations",
        "pazeeefortnitebackon74moveclip",
        "Fortnite Back On 74 Move",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
}
CustomDP.AnimalEmotes = {}
CustomDP.Exits = {}
CustomDP.Emotes = {
    ["psnow1"] = {
        "psnow1@animations",
        "psnow1clip",
        "Snow Angels 1",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["psnow2"] = {
        "psnow2@animations",
        "psnow2clip",
        "Snow Angels 2",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["psnow3"] = {
        "psnow3@animations",
        "psnow3clip",
        "Snow Angels 3",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["psnow4"] = {
        "psnow4@animations",
        "psnow4clip",
        "Snow Crawl 1",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["psnow5"] = {
        "psnow5@animations",
        "psnow5clip",
        "Snow Crawl 2",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["psnow6"] = {
        "psnow6@animations",
        "psnow6clip",
        "Snow Feel",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["psnow7"] = {
        "psnow7@animations",
        "psnow7clip",
        "Snow Buried 1",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["psnow8"] = {
        "psnow8@animations",
        "psnow8clip",
        "Snow Buried 2",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["psnow9"] = {
        "psnow9@animations",
        "psnow9clip",
        "Snow Buried 3",
        AnimationOptions = {
            EmoteLoop = true,
        }
    },
    ["psnow10"] = {
        "psnow10@animations",
        "psnow10clip",
        "Snow Buried 4",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["psnow11"] = {
        "psnow11@animations",
        "psnow11clip",
        "Snow Sliding 1",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["psnow12"] = {
        "psnow12@animations",
        "psnow12clip",
        "Snow Sliding 2",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["psnow13"] = {
        "psnow13@animations",
        "psnow13clip",
        "Snow Sliding 3",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["psnow14"] = {
        "psnow14@animations",
        "psnow14clip",
        "Snow Sliding 4",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["psnow15"] = {
        "psnow15@animations",
        "psnow15clip",
        "Snow Sliding 5",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["psnow7female"] = {
        "psnow7b@animations",
        "psnow7bclip",
        "Snow Buried 1 Female",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["psnow8female"] = {
        "psnow8b@animations",
        "psnow8bclip",
        "Snow Buried 2 Female",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["psnow9female"] = {
        "psnow9b@animations",
        "psnow9bclip",
        "Snow Buried 3 Female",
        AnimationOptions = {
            EmoteLoop = true,
        }
    },
    ["psnow10female"] = {
        "psnow10b@animations",
        "psnow10bclip",
        "Snow Buried 4 Female",
        AnimationOptions = {
            EmoteLoop = true
        }
    },
    ["hidef"] = {
        "hidef@animations",
        "hidefclip",
        "Hide Forward",
        AnimationOptions = {
            EmoteLoop = true,
        }
    },
    ["hideb"] = {
        "hideb@animations",
        "hidebclip",
        "Hide Backward",
        AnimationOptions = {
            EmoteLoop = true,
        }
    },
    ["hider"] = {
        "hider@animations",
        "hiderclip",
        "Hide Right",
        AnimationOptions = {
            EmoteLoop = true,
        }
    },
    ["hidel"] = {
        "hidel@animations",
        "hidelclip",
        "Hide Left",
        AnimationOptions = {
            EmoteLoop = true,
        }
    },
    ["hidecarf"] = {
        "hidecarf@animations",
        "hidecarfclip",
        "Hide Car Forward",
        AnimationOptions = {
            EmoteLoop = true,
        }
    },
    ["hidecarb"] = {
        "hidecarb@animations",
        "hidecarbclip",
        "Hide Car Backward",
        AnimationOptions = {
            EmoteLoop = true,
        }
    },
    ["hidecarr"] = {
        "hidecarr@animations",
        "hidecarrclip",
        "Hide Car Right",
        AnimationOptions = {
            EmoteLoop = true,
        }
    },
    ["hidecarl"] = {
        "hidecarl@animations",
        "hidecarlclip",
        "Hide Car Left",
        AnimationOptions = {
            EmoteLoop = true,
        }
    },
    ["hidepole"] = {
        "hidepole@animations",
        "hidepoleclip",
        "Hide Pole",
        AnimationOptions = {
            EmoteLoop = true,
        }
    },
    ["hidepolehigh"] = {
        "hidepolehigh@animations",
        "hidepolehighclip",
        "Hide Pole High",
        AnimationOptions = {
            EmoteLoop = true,
        }
    },
    ["hidepolehigh2"] = {
        "hidepolehigh2@animations",
        "hidepolehigh2clip",
        "Hide Pole High 2",
        AnimationOptions = {
            EmoteLoop = true,
        }
    },
    ["hidepoleveryhigh"] = {
        "hidepoleveryhigh@animations",
        "hidepoleveryhighclip",
        "Hide Pole Very High",
        AnimationOptions = {
            EmoteLoop = true,
        }
    },
    ["hidepoleveryhigh2"] = {
        "hidepoleveryhigh2@animations",
        "hidepoleveryhigh2clip",
        "Hide Pole Very High 2",
        AnimationOptions = {
            EmoteLoop = true,
        }
    },
    ["bigdawgs"] = {
        "bigdawgs@animations",
        "bigdawgsclip",
        "Big Dawgs",
        AnimationOptions = {
            EmoteLoop = true,
        }
    },
    ["cr7siu"] = {
        "cr7siu@animations",
        "cr7siu_clip",
        "Cristiano Ronaldo Siuuu",
        AnimationOptions = {
            EmoteLoop = true,
        }
    },
    ["pavehcar1l"] = {
        "pavehcar1l@animations",
        "pavehcar1lclip",
        "Veh Sit-Up Left",
        AnimationOptions = {
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["pavehcar1r"] = {
        "pavehcar1r@animations",
        "pavehcar1rclip",
        "Veh Sit-Up Right",
        AnimationOptions = {
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["pavehcar2r"] = {
        "pavehcar2r@animations",
        "pavehcar2rclip",
        "Veh Hold On Tight Right",
        AnimationOptions = {
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["pavehcar2l"] = {
        "pavehcar2l@animations",
        "pavehcar2lclip",
        "Veh Hold On Tight Left",
        AnimationOptions = {
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["pavehcar3r"] = {
        "pavehcar3r@animations",
        "pavehcar3rclip",
        "Veh Sit Relaxs Right",
        AnimationOptions = {
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["pavehcar3l"] = {
        "pavehcar3l@animations",
        "pavehcar3lclip",
        "Veh Sit Relaxs Left",
        AnimationOptions = {
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["pavehcar4r"] = {
        "pavehcar4r@animations",
        "pavehcar4rclip",
        "Veh Sit and Wave Right",
        AnimationOptions = {
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["pavehcar4l"] = {
        "pavehcar4l@animations",
        "pavehcar4lclip",
        "Veh Sit Cool Left",
        AnimationOptions = {
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["pavehcar5r"] = {
        "pavehcar5r@animations",
        "pavehcar5rclip",
        "Veh Rock And Roll Right",
        AnimationOptions = {
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["pavehcar5l"] = {
        "pavehcar5l@animations",
        "pavehcar5lclip",
        "Veh Rock And Roll Left",
        AnimationOptions = {
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["pavehcar6r"] = {
        "pavehcar6r@animations",
        "pavehcar6rclip",
        "Veh Sit Relaxs Roof Right",
        AnimationOptions = {
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["pavehcar6l"] = {
        "pavehcar6l@animations",
        "pavehcar6lclip",
        "Veh Sit Relaxs Roof Left",
        AnimationOptions = {
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
	["pavehcar7r"] = {
        "pavehcar7r@animations",
        "pavehcar7rclip",
        "Veh Sit Happy Right",
        AnimationOptions = {
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["pavehcar7l"] = {
        "pavehcar7l@animations",
        "pavehcar7lclip",
        "Veh Sit Happy Left",
        AnimationOptions = {
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["pavehcar8r"] = {
        "pavehcar8r@animations",
        "pavehcar8rclip",
        "Veh Sleep Right",
        AnimationOptions = {
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["pavehcar8l"] = {
        "pavehcar8l@animations",
        "pavehcar8lclip",
        "Veh Sleep Left",
        AnimationOptions = {
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["pavehcar9r"] = {
        "pavehcar9r@animations",
        "pavehcar9rclip",
        "Veh Take Video Right",
        AnimationOptions = {
            Prop = "prop_phone_ing",
            PropBone = 28422,
            PropPlacement = {
                0.05,
                0.0100,
                0.060,
                -174.961,
                149.618,
                8.649,
            },
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["pavehcar9l"] = {
        "pavehcar9l@animations",
        "pavehcar9lclip",
        "Veh Take Video Left",
        AnimationOptions = {
            Prop = "prop_phone_ing",
            PropBone = 58866,
            PropPlacement = {
                0.07,
                -0.0500,
                0.010,
                -105.33,
                -168.30,
                48.97,
            },
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["pavehcar10"] = {
        "pavehcar10@animations",
        "pavehcar10clip",
        "Veh Sit Enjoy Lucia",
        AnimationOptions = {
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["pbvehcar1"] = {
        "pbvehcar1@animations",
        "pbvehcar1clip",
        "Veh Sit Here I Am",
        AnimationOptions = {
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["pbvehcar2"] = {
        "pbvehcar2@animations",
        "pbvehcar2clip",
        "Veh Sit Enjoy The Wind",
        AnimationOptions = {
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["pbvehcar3r"] = {
        "pbvehcar3r@animations",
        "pbvehcar3rclip",
        "Veh Sit Enjoy The Ride Right",
        AnimationOptions = {
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["pbvehcar3l"] = {
        "pbvehcar3l@animations",
        "pbvehcar3lclip",
        "Veh Sit Enjoy The Ride Left",
        AnimationOptions = {
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["pbvehcar4r"] = {
        "pbvehcar4r@animations",
        "pbvehcar4rclip",
        "Veh Sit Enjoy The Ride 2 Right",
        AnimationOptions = {
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["pbvehcar4l"] = {
        "pbvehcar4l@animations",
        "pbvehcar4lclip",
        "Veh Sit Enjoy The Ride 2 Left",
        AnimationOptions = {
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["pbvehcar5r"] = {
        "pbvehcar5r@animations",
        "pbvehcar5rclip",
        "Veh Sit Looking The View Right",
        AnimationOptions = {
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["pbvehcar5l"] = {
        "pbvehcar5l@animations",
        "pbvehcar5lclip",
        "Veh Sit Looking The View Left",
        AnimationOptions = {
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["pbvehcar6r"] = {
        "pbvehcar6r@animations",
        "pbvehcar6rclip",
        "Veh Twerk Right",
        AnimationOptions = {
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["pbvehcar6l"] = {
        "pbvehcar6l@animations",
        "pbvehcar6lclip",
        "Veh Twerk Left",
        AnimationOptions = {
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
	["pbvehcar7l"] = {
        "pbvehcar7l@animations",
        "pbvehcar7lclip",
        "Veh Standing At The Driver Left",
        AnimationOptions = {
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["pbvehcar8"] = {
        "pbvehcar8@animations",
        "pbvehcar8clip",
        "Veh Sleep On The Roof",
        AnimationOptions = {
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["pbvehcar9"] = {
        "pbvehcar9@animations",
        "pbvehcar9clip",
        "Veh Sit Relaxs On The Roof",
        AnimationOptions = {
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["pbvehcar10"] = {
        "pbvehcar10@animations",
        "pbvehcar10clip",
        "Veh Relaxs On The Roof",
        AnimationOptions = {
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["pcvehcar1"] = {
        "pcvehcar1@animations",
        "pcvehcar1clip",
        "Veh Sit Enjoy On The Roof",
        AnimationOptions = {
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["pcvehcar2r"] = {
        "pcvehcar2r@animations",
        "pcvehcar2rclip",
        "Veh Sit Trunk Right",
        AnimationOptions = {
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["pcvehcar2l"] = {
        "pcvehcar2l@animations",
        "pcvehcar2lclip",
        "Veh Sit Trunk Left",
        AnimationOptions = {
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["pcvehcar3r"] = {
        "pcvehcar3r@animations",
        "pcvehcar3rclip",
        "Veh Sit Trunk Lower Right",
        AnimationOptions = {
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["pcvehcar3l"] = {
        "pcvehcar3l@animations",
        "pcvehcar3lclip",
        "Veh Sit Trunk Lower Left",
        AnimationOptions = {
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["pcvehcar4r"] = {
        "pcvehcar4r@animations",
        "pcvehcar4rclip",
        "Veh Fly Right",
        AnimationOptions = {
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["pcvehcar4l"] = {
        "pcvehcar4l@animations",
        "pcvehcar4lclip",
        "Veh Fly Left",
        AnimationOptions = {
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["pcvehcar5"] = {
        "pcvehcar5@animations",
        "pcvehcar5clip",
        "Veh Fly Random",
        AnimationOptions = {
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["pcvehcar6"] = {
        "pcvehcar6@animations",
        "pcvehcar6clip",
        "Veh Fly Higher",
        AnimationOptions = {
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["pcvehcar7"] = {
        "pcvehcar7@animations",
        "pcvehcar7clip",
        "Veh Motorcycle Hold On Tight",
        AnimationOptions = {
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["pcvehcar8"] = {
        "pcvehcar8@animations",
        "pcvehcar8clip",
        "Veh Motorcycle Two Gun",
        AnimationOptions = {
            Prop = 'w_pi_pistol',
            PropBone = 26611,
            PropPlacement = {
                0.07,
                -.01,
                0.01,
                -29.999,
                0.0,
                10.000
            },
            SecondProp = 'w_pi_pistol',
            SecondPropBone = 58867,
            SecondPropPlacement = {
                0.07,
                0.01,
                0.01,
                29.999,
                0.0,
                -10.000
            },
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["pcvehcar9"] = {
        "pcvehcar9@animations",
        "pcvehcar9clip",
        "Veh Motorcycle Sit Facing Back",
        AnimationOptions = {
            EmoteLoop = true,
            EmoteMoving = false,
			FullBody = true
        }
    },
    ["ptrashcana"] = {
        "ptrashcana@animations",
        "ptrashcanaclip",
        "Trash Can Hide A Loop",
        AnimationOptions = {
            Prop = "prop_recyclebin_03_a",
            PropBone = 52301,
            PropPlacement = {
                0.08,
                -0.0900,
               -0.10,
                -90,
                0,
                20.000,
            },
            EmoteLoop = true
        }
    },
    ["ptrashcanb"] = {
        "ptrashcanb@animations",
        "ptrashcanbclip",
        "Trash Can Hide A Look & Peek",
        AnimationOptions = {
            Prop = "prop_recyclebin_03_a",
            PropBone = 52301,
            PropPlacement = {
                0.06,
                -0.1170,
               -0.090,
                -90,
                0,
                18.00,
            },
            EmoteLoop = true
        }
    },
    ["ptrashcanc"] = {
        "ptrashcanc@animations",
        "ptrashcancclip",
        "Trash Can Hide A Peek",
        AnimationOptions = {
            Prop = "prop_recyclebin_03_a",
            PropBone = 52301,
            PropPlacement = {
                0.08,
                -0.0900,
               -0.10,
                -90,
                0,
                20.000,
            },
            EmoteLoop = true
        }
    },
    ["ptrashcand"] = {
        "ptrashcand@animations",
        "ptrashcandclip",
        "Trash Can Stuck Struggle",
        AnimationOptions = {
            Prop = "prop_recyclebin_03_a",
            PropBone = 0,
            PropPlacement = {
                -0.02,
                -0.5900,
               0.030,
                -88.000,
                0,
                0,
            },
            EmoteLoop = true
        }
    },
    ["ptrashcane"] = {
        "ptrashcane@animations",
        "ptrashcaneclip",
        "Trash Can Stuck Upside Down",
        AnimationOptions = {
            Prop = "prop_recyclebin_03_a",
            PropBone = 39317,
            PropPlacement = {
                0,
                -0.100,
               0.0,
                -90.000,
                0,
                -20,
            },
            EmoteLoop = true
        }
    },
    ["ptrashcanf"] = {
        "ptrashcanf@animations",
        "ptrashcanfclip",
        "Trash Can Stuck Flip",
        AnimationOptions = {
            Prop = "prop_recyclebin_03_a",
            PropBone = 18905,
            PropPlacement = {
                0.15,
                0.100,
                0.050,
                -9.400,
                -160.280,
                -3.40,
            },
            EmoteLoop = true
        }
    },
    ["ptrashcang"] = {
        "ptrashcang@animations",
        "ptrashcangclip",
        "Trash Can Full Stuck Upside Down",
        AnimationOptions = {
            Prop = "prop_recyclebin_03_a",
            PropBone = 18905,
            PropPlacement = {
                0.13,
                0.1100,
                0.050,
                -9.51,
                -162.25,
                -3.0,
            },
            EmoteLoop = true
        }
    },
    ["ptrashcanh"] = {
        "ptrashcanh@animations",
        "ptrashcanhclip",
        "Trash Can Hide B Loop",
        AnimationOptions = {
            Prop = "prop_recyclebin_03_a",
            PropBone = 0,
            PropPlacement = {
                0.0,
                0.100,
                0.725,
                0.000,
                180,
                0,
            },
            EmoteLoop = true
			
        }
    },
    ["ptrashcani"] = {
        "ptrashcani@animations",
        "ptrashcaniclip",
        "Trash Can Hide B Sneaky",
        AnimationOptions = {
            Prop = "prop_recyclebin_03_a",
            PropBone = 0,
            PropPlacement = {
                0.0,
                0.100,
                0.80,
                0.000,
                180,
                0,
            },
            EmoteLoop = true
        }
    },
    ["ptrashcanj"] = {
        "ptrashcanj@animations",
        "ptrashcanjclip",
        "Trash Can Hide B Walk",
        AnimationOptions = {
            Prop = "prop_recyclebin_03_a",
            PropBone = 0,
            PropPlacement = {
                0.005,
                0.125,
                0.780,
                0.000,
                180,
                0,
            },
            EmoteLoop = true
        }
    },
    ["ptrashcank"] = {
        "ptrashcank@animations",
        "ptrashcankclip",
        "Trash Can Hide B Panic",
        AnimationOptions = {
            Prop = "prop_recyclebin_03_a",
            PropBone = 0,
            PropPlacement = {
                0.005,
                0.125,
                0.780,
                0.000,
                180,
                0,
            },
            EmoteLoop = true
        }
    },
    ["ptrashcanl"] = {
        "ptrashcanl@animations",
        "ptrashcanlclip",
        "Trash Can Hide B Run For Your Lifeee",
        AnimationOptions = {
            Prop = "prop_recyclebin_03_a",
            PropBone = 24817,
            PropPlacement = {
                0.580,
                0.1500,
                0,
                10.000,
                -90,
                0,
            },
            EmoteMoving = true,
            EmoteLoop = true
			
        }
    },
    ["ptrashcanm"] = {
        "ptrashcanm@animations",
        "ptrashcanmclip",
        "Trash Can Dance Happy 1",
        AnimationOptions = {
            Prop = "prop_recyclebin_03_a",
            PropBone = 24817,
            PropPlacement = {
                0.70,
                0.175,
                0,
                10.000,
                -90,
                0,
            },
            EmoteLoop = true
			
        }
    },
    ["ptrashcann"] = {
        "ptrashcann@animations",
        "ptrashcannclip",
        "Trash Can Dance Happy 2",
        AnimationOptions = {
            Prop = "prop_recyclebin_03_a",
            PropBone = 24817,
            PropPlacement = {
                0.70,
                0.175,
                0,
                10.000,
                -90,
                0,
            },
            EmoteLoop = true
			
        }
    },
    ["ptrashcano"] = {
        "ptrashcano@animations",
        "ptrashcanoclip",
        "Trash Can Dance Happy 3",
        AnimationOptions = {
            Prop = "prop_recyclebin_03_a",
            PropBone = 24817,
            PropPlacement = {
                0.70,
                0.175,
                0,
                10.000,
                -90,
                0,
            },
            EmoteLoop = true
			
        }
    },
    ["ptrashcanp"] = {
        "ptrashcanp@animations",
        "ptrashcanpclip",
        "Trash Can Cool Lean",
        AnimationOptions = {
            Prop = "prop_recyclebin_03_a",
            PropBone = 52301,
            PropPlacement = {
                0.08,
                -0.0900,
               -0.10,
                -90,
                0,
                20.000,
            },
            EmoteLoop = true
        }
    },
    ["ptrashcanq"] = {
        "ptrashcanq@animations",
        "ptrashcanqclip",
        "Trash Can Jump Slow",
        AnimationOptions = {
            Prop = "prop_recyclebin_03_a",
            PropBone = 52301,
            PropPlacement = {
                0.08,
                -0.0900,
               -0.10,
                -90,
                0,
                20.000,
            },
            EmoteLoop = true
        }
    },
    ["ptrashcanr"] = {
        "ptrashcanr@animations",
        "ptrashcanrclip",
        "Trash Can Jump Fast",
        AnimationOptions = {
            Prop = "prop_recyclebin_03_a",
            PropBone = 52301,
            PropPlacement = {
                0.08,
                -0.0900,
               -0.10,
                -90,
                0,
                20.000,
            },
            EmoteLoop = true
        }
    },
    ["ptrashcans"] = {
        "ptrashcans@animations",
        "ptrashcansclip",
        "Trash Can Jump Long",
        AnimationOptions = {
            Prop = "prop_recyclebin_03_a",
            PropBone = 52301,
            PropPlacement = {
                0.08,
                -0.0900,
               -0.10,
                -90,
                0,
                20.000,
            },
            EmoteLoop = true
        }
    },
    ["ptrashcant"] = {
        "ptrashcant@animations",
        "ptrashcantclip",
        "Trash Can Turtle Stuck",
        AnimationOptions = {
            Prop = "prop_recyclebin_03_a",
            PropBone = 0,
            PropPlacement = {
                0.0,
                -0.4100,
               -0.340,
                -40,
                0,
                0.000,
            },
            EmoteLoop = true
        }
    },
    ["ptrashcanu"] = {
        "ptrashcanu@animations",
        "ptrashcanuclip",
        "Trash Can Turtle Enjoy",
        AnimationOptions = {
            Prop = "prop_recyclebin_03_a",
            PropBone = 0,
            PropPlacement = {
                0.0,
                -0.4100,
               -0.340,
                -40,
                0,
                0.000,
            },
            EmoteLoop = true
        }
    },
    ["ptrashcanv"] = {
        "ptrashcanv@animations",
        "ptrashcanvclip",
        "Trash Can Turtle Walk Struggle",
        AnimationOptions = {
            Prop = "prop_recyclebin_03_a",
            PropBone = 0,
            PropPlacement = {
                0.0,
                -0.470,
               -0.390,
                -40,
                0,
                0.000,
            },
            EmoteLoop = true
        }
    },
    ["ptrashcanw"] = {
        "ptrashcanw@animations",
        "ptrashcanwclip",
        "Trash Can Turtle Walk Normal",
        AnimationOptions = {
            Prop = "prop_recyclebin_03_a",
            PropBone = 0,
            PropPlacement = {
                0.0,
                -0.400,
               -0.440,
                -35,
                0,
                0.000,
            },
            EmoteLoop = true
        }
    },
    ["ptrashcanx"] = {
        "ptrashcanx@animations",
        "ptrashcanxclip",
        "Trash Can Turtle Walk Panic",
        AnimationOptions = {
            Prop = "prop_recyclebin_03_a",
            PropBone = 0,
            PropPlacement = {
                0.0,
                -0.400,
               -0.440,
                -35,
                0,
                0.000,
            },
            EmoteLoop = true
        }
    },
    ["ptrashcany"] = {
        "ptrashcany@animations",
        "ptrashcanyclip",
        "Trash Can Hide C Look Around",
        AnimationOptions = {
            Prop = "prop_recyclebin_03_a",
            PropBone = 0,
            PropPlacement = {
                0.0,
                -0.0600,
               -0.230,
                -35,
                0,
                0.000,
            },
            EmoteLoop = true
        }
    },
    ["ptrashcanz"] = {
        "ptrashcanz@animations",
        "ptrashcanzclip",
        "Trash Can Hide C Loop",
        AnimationOptions = {
            Prop = "prop_recyclebin_03_a",
            PropBone = 52301,
            PropPlacement = {
                0.03,
                -0.1300,
                0.02,
                -98,
                0,
                20.000,
            },
            EmoteLoop = true
        }
    },
    ["ptrashcanza"] = {
        "ptrashcanza@animations",
        "ptrashcanzaclip",
        "Trash Can Spin",
        AnimationOptions = {
            Prop = "prop_recyclebin_03_a",
            PropBone = 52301,
            PropPlacement = {
                0.03,
                -0.1300,
                0.02,
                -98,
                0,
                20.000,
            },
            EmoteLoop = true
        }
    },
    ["ptrashcanzb"] = {
        "ptrashcanzb@animations",
        "ptrashcanzbclip",
        "Trash Can Spin Fast",
        AnimationOptions = {
            Prop = "prop_recyclebin_03_a",
            PropBone = 52301,
            PropPlacement = {
                0.03,
                -0.1300,
                0.02,
                -98,
                0,
                20.000,
            },
            EmoteLoop = true
        }
    },
    ["ptrashcanzc"] = {
        "ptrashcanzc@animations",
        "ptrashcanzcclip",
        "Trash Can Roll Slow",
        AnimationOptions = {
            Prop = "prop_recyclebin_03_a",
            PropBone = 0,
            PropPlacement = {
                0.0,
                -0.0600,
               -0.230,
                -35,
                0,
                0.000,
            },
            EmoteLoop = true
        }
    },
    ["ptrashcanzd"] = {
        "ptrashcanzd@animations",
        "ptrashcanzdclip",
        "Trash Can Roll Fast",
        AnimationOptions = {
            Prop = "prop_recyclebin_03_a",
            PropBone = 0,
            PropPlacement = {
                0.0,
                -0.0600,
               -0.230,
                -35,
                0,
                0.000,
            },
            EmoteLoop = true
        }
    },
    ["ptrashcanze"] = {
        "ptrashcanze@animations",
        "ptrashcanzeclip",
        "Trash Can Roll Out Of Control",
        AnimationOptions = {
            Prop = "prop_recyclebin_03_a",
            PropBone = 0,
            PropPlacement = {
                0.0,
                -0.0600,
               -0.230,
                -35,
                0,
                0.000,
            },
            EmoteLoop = true
        }
    },
    ["ptrashcanzf"] = {
        "ptrashcanzf@animations",
        "ptrashcanzfclip",
        "Trash Can Cool Sit 1",
        AnimationOptions = {
            Prop = "prop_recyclebin_03_a",
            PropBone = 0,
            PropPlacement = {
                0.0,
                0.10,
               -0.150,
                164.9,
                0,
                0.000,
            },
            EmoteLoop = true
        }
    },
    ["ptrashcanzg"] = {
        "ptrashcanzg@animations",
        "ptrashcanzgclip",
        "Trash Can Cool Sit 2",
        AnimationOptions = {
            Prop = "prop_recyclebin_03_a",
            PropBone = 0,
            PropPlacement = {
                0.0,
                0.10,
               -0.150,
                169.9,
                0,
                0.000,
            },
            EmoteLoop = true
        }
    },
    ["ptrashcanzh"] = {
        "ptrashcanzh@animations",
        "ptrashcanzhclip",
        "Trash Can Impossible Pose 1",
        AnimationOptions = {
            Prop = "prop_recyclebin_03_a",
            PropBone = 52301,
            PropPlacement = {
                0.120,
                -0.980,
                00,
                -90,
                0,
                -20.000,
            },
            SecondProp = 'prop_recyclebin_03_a',
            SecondPropBone = 64097,
            SecondPropPlacement = {
                0.20,
                -0.20,
                0.000,
              -38.255,
               74.42,
                12.700,
            },
            EmoteLoop = true
        }
    },
    ["ptrashcanzi"] = {
        "ptrashcanzi@animations",
        "ptrashcanziclip",
        "Trash Can Impossible Pose 2",
        AnimationOptions = {
            Prop = "prop_recyclebin_03_a",
            PropBone = 14201,
            PropPlacement = {
                0.10,
                -0.050,
                0.0,
                90,
                0,
                20,
            },
            SecondProp = 'prop_recyclebin_03_a',
            SecondPropBone = 31086,
            SecondPropPlacement = {
                0.140,
                0.10,
                0.000,
                -90,
                0,
                -40,
            },
            EmoteLoop = true
        }
    },
    ["ptrashcanzj"] = {
        "ptrashcanzj@animations",
        "ptrashcanzjclip",
        "Trash Can Impossible Pose 3",
        AnimationOptions = {
            Prop = "prop_recyclebin_03_a",
            PropBone = 52301,
            PropPlacement = {
                0.110,
                -0.050,
                -0.150,
                90,
                0,
                20.000,
            },
            SecondProp = 'prop_recyclebin_03_a',
            SecondPropBone = 24817,
            SecondPropPlacement = {
                0.09,
                -0.10,
                0.000,
                90,
                0,
                5.0,
            },
            EmoteLoop = true
        }
    },
    ["ptrashcanzk"] = {
        "ptrashcanzk@animations",
        "ptrashcanzkclip",
        "Trash Can Carry 1",
        AnimationOptions = {
            Prop = "prop_bin_07d",
            PropBone = 57005,
            PropPlacement = {
                0.090,
                -0.640,
                -0.37,
                -85.076,
                -0.867,
                -0.037,
            },
			EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["ptrashcanzl"] = {
        "ptrashcanzl@animations",
        "ptrashcanzlclip",
        "Trash Can Carry 2",
        AnimationOptions = {
            Prop = "prop_bin_07d",
            PropBone = 57005,
            PropPlacement = {
                -0.17,
                -0.370,
                -0.370,
                -79.372,
                3.616,
                -19.683,
            },
            SecondProp = 'prop_bin_07d',
            SecondPropBone = 18905,
            SecondPropPlacement = {
                -0.15,
                -0.34,
                0.370,
                -100.62,
                -3.616,
                -19.683,
            },
			EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["ptrashcanzm"] = {
        "ptrashcanzm@animations",
        "ptrashcanzmclip",
        "Trash Can Carry Overhead",
        AnimationOptions = {
            Prop = "prop_bin_07d",
            PropBone = 57005,
            PropPlacement = {
                -0.090,
                0.060,
                -0.31,
                0,
                79.999,
                0,
            },
			EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["ptrashcanzn"] = {
        "ptrashcanzn@animations",
        "ptrashcanznclip",
        "Trash Can I Can't See",
        AnimationOptions = {
            Prop = "prop_bin_07d",
            PropBone = 24818,
            PropPlacement = {
                1.04,
                0.10,
                0.0,
                0,
                -90,
                -10,
            },
			EmoteMoving = true,
            EmoteLoop = true
        }
    },
    ["ptrophy"] = {
        "ptrophy@animations",
        "ptrophyclip",
        "Hold Trophy",
        AnimationOptions = {
            Prop = "xs_prop_trophy_cup_01a",
            PropBone = 57005,
            PropPlacement = {
                0.010,
                -0.080,
                -0.1,
                -43.583,
                54.555,
                15.5168,
            },
            EmoteLoop = true
        }
    },
    ["ptrophyrun"] = {
        "ptrophyrun@animations",
        "ptrophyrunclip",
        "Hold Trophy Run",
        AnimationOptions = {
            Prop = "xs_prop_trophy_cup_01a",
            PropBone = 57005,
            PropPlacement = {
                0.010,
                -0.080,
                -0.1,
                -43.583,
                54.555,
                15.5168,
            },
            EmoteLoop = true,
			EmoteMoving = true
        }
    },
}
CustomDP.PropEmotes = {}

-----------------------------------------------------------------------------------------
--| I don't think you should change the code below unless you know what you are doing |--
-----------------------------------------------------------------------------------------

-- Create separate tables for custom emotes
RP.CustomEmotes = {}
RP.CustomDances = {}
RP.CustomPropEmotes = {}
RP.CustomAnimalEmotes = {}
RP.CustomShared = {}

function LoadAddonEmotes()
    for arrayName, array in pairs(CustomDP) do
        if RP[arrayName] then
            for emoteName, emoteData in pairs(array) do
                -- Add to main tables for backward compatibility
                RP[arrayName][emoteName] = emoteData

                -- Also add to custom tables for separate menu sections
                if arrayName == "Emotes" then
                    RP.CustomEmotes[emoteName] = emoteData
                elseif arrayName == "Dances" then
                    RP.CustomDances[emoteName] = emoteData
                elseif arrayName == "PropEmotes" then
                    RP.CustomPropEmotes[emoteName] = emoteData
                elseif arrayName == "AnimalEmotes" then
                    RP.CustomAnimalEmotes[emoteName] = emoteData
                elseif arrayName == "Shared" then
                    RP.CustomShared[emoteName] = emoteData
                end
            end
        end
    end
    -- Free memory
    CustomDP = nil
end
