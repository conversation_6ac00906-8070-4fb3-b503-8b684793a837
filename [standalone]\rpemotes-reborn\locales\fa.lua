Locales['fa'] = {
 -- persian 🇮🇷
        ['emotes'] = "~h~~p~ Emotes 🎬",
        ['danceemotes'] = "~h~~p~ 🕺 raghs Emotes",
        ['animalemotes'] = "~h~~p~ 🐩 hevanat Emotes",
        ['propemotes'] = "~h~~p~ 📦 tekye dadan Emotes",
        ['favoriteemotes'] = "~h~~y~ 🌟 alaghemandiha",
        ['favoriteinfo'] = "yek emote ra baray ezafe kardan be alaghe mandi ha entekhab konid.",
        ['rfavorite'] = "bazneshani alaghe mandi ha",
        ['prop2info'] = "❓ bad az tekye dadan be yek ja motavaghef mishavad",
        ['set'] = "Set (",
        ['setboundemote'] = ") che kelid baray dastressi sari entekhab shavad?",
        ['newsetemote'] = "~w~ baray dastresi sari, press ~g~CapsLock~w~ baray ejra",
        ['cancelemote'] = "~h~~r~ laghv emote🚷",
        ['cancelemoteinfo'] = "~r~X~w~ baray laghv emote ",
        ['walkingstyles'] = "~h~~p~ style rah raftan🚶🏻‍♂️",
        ['resetdef'] = "bazneshani pishfarz",
        ['normalreset'] = "~h~~r~ Mamoli (Pishfarz)",
        ['moods'] = "~h~~p~ halat sorat 😒",
        ['infoupdate'] = "~h~~g~ pishnahad 🤝🏻",
        ['infoupdateav'] = "etelaate (berozresani mojod)",
        ['infoupdateavtext'] = "update jadid vojod darad baray daryaft ~y~https://github.com/alberttheprince/rpemotes-reborn~w~",
        ['suggestions'] = "pishnahad?",
        ['suggestionsinfo'] = "~r~Noor_Nahas~s~ on FiveM forums for any feature/emote suggestions! ✉️",
        ['notvaliddance'] = "raghsi vojod nadarad.",
        ['notvalidemote'] = "emote motabar nist.",
        ['nocancel'] = "emote baray laghv vojod nadarad.",
        ['maleonly'] = "in emote faghat baray mardan ast motasefam!",
        ['emotemenucmd'] = "ba farman /emotemenu be menu emote ha miravid",
        ['shareemotes'] = "👫 eshterak emote",
        ['shareemotesinfo'] = "peyvastan afrad atraf be in emote",
        ['sharedanceemotes'] = "🕺 eshtrak raghs",
        ['notvalidsharedemote'] = "in emote baray eshtrak motabar nist.",
        ['sentrequestto'] = "ersal darkhast ~y~",
        ['nobodyclose'] = "hichkas nist ~r~bastan~w~ kafi nist.",
        ['doyouwanna'] = "~y~Y~w~ baray ghabol, ~r~L~w~ baray rad kardan  (~g~",
        ['refuseemote'] = "Emote rad shod.",
        ['makenearby'] = "sakht baray bazi ba atrafian",
        ['useleafblower'] = "entekhab ~y~G~w~ baray entekhab barg rob.",
        ['camera'] = "entekhab ~y~G~w~ baray entekhab cheragh.",
        ['makeitrain'] = "entekhab ~y~G~w~ baray sakht chatr.",
        ['pee'] = "negahdarid ~y~G~w~ baray shashidan.",
        ['spraychamp'] = "negahdarid ~y~G~w~ baray rikhtan mashrob",
        ['stun'] = "Press ~y~G~w~ baray 'use' tofang bihoshi.",
        ['smoke'] = "Press ~y~G~w~ to smoke.",
        ['vape'] = "entekhab ~y~G~w~ to vape.",
        ['candle'] = "press ~y~G~w~ to light candle.",
        ['boundto'] = "Bound (%s) baray %s",
        ['currentlyboundemotes'] = " emote mahdod ast:",
        ['notvalidkey'] = "vojod nadarad.",
        ['keybinds'] = "🔢 kilid sari",
        ['keybindsinfo'] = "entekhab",
        ['searchemotes'] = "~h~~y~ 🔍 jostjo Emotes",
        ['searchinputtitle'] = "jostojo:",
        ['searchmenudesc'] = "result(s) for",
        ['searchnoresult'] = "No results for search",
        ['searchshifttofav'] = "Hold L-Shift and press enter to set as favorite.",
        ['searchcantsetfav'] = "Shared emotes cannot be set as favorites.",
        ['invalidvariation'] = "Invalid texture variation. Valid selections are: %s",
        ['firework'] = "Press ~y~G~w~ to use the firework",
        ['poop'] = "Press ~y~G~w~ to poop",
        ['puke'] = "Press ~y~G~w~ to vomit",
		['cut'] = "Press ~y~G~w~ to cut",
        ['btn_select'] = "Select",
        ['btn_back'] = "Back",
        ['btn_switch'] = "Movement",
        ['btn_increment'] = "Increment",
        ['dead'] = "You can't use emotes while dead!",
        ['swimming'] = "You can't use emotes while swimming",
        ['notvalidpet'] = "RUH ROH! Incorrect ped model detected 🐕!",
        ['animaldisabled'] = "Sorry! Animal emotes are disabled on this server",
        ['adultemotedisabled'] = "Bonk! Adult emotes disabled 🔞",
        ['toggle_instructions'] = "Toggle the instructions",
        ['exit_binoculars'] = "Exit binoculars",
        ['toggle_binoculars_vision'] = "Toggle between vision modes",
        ['exit_news'] = "Exit News Camera",
        ['toggle_news_vision'] = "Toggle between vision modes",
        ['edit_values_newscam'] = "Edit the news text",
        ['not_in_a_vehicle'] = "You can't play this animation while in a vehicle",
        ['in_a_vehicle'] = "You can only play this animation while in a vehicle 🚷",
        ['no_anim_crawling'] = "You can't play animations while crawling",
        ['no_anim_right_now'] = "You can't play an animation right now",
    
}
