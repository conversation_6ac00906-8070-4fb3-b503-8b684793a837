Locales['pl'] = {
 -- Polish 🇵🇱
        ['emotes'] = "~h~~p~ Animacje 🎬",
        ['danceemotes'] = "~h~~p~ 🕺 Ta<PERSON>",
        ['animalemotes'] = "~h~~p~ 🐩 Animacje zwierząt",
        ['propemotes'] = "~h~~p~ 📦 Animacje z propami",
        ['favoriteemotes'] = "~h~~y~ 🌟 Ulubione",
        ['favoriteinfo'] = "Wybierz animację i ustaw ją jako ulubioną.",
        ['rfavorite'] = "Zresetuj ulubione animacje",
        ['prop2info'] = "❓ Animacje z propami są zlokalizowane na samym końcu listy",
        ['set'] = "Ustaw (",
        ['setboundemote'] = ") jako Twoją przypisaną animację?",
        ['newsetemote'] = "~w~ jest teraz Twoją przypisaną animacją, wciśnij ~g~CapsLock~w~ by jej <PERSON>.",
        ['cancelemote'] = "~h~~r~ Anuluj animację 🚷",
        ['cancelemoteinfo'] = "~r~X~w~ Anuluje aktualnie graną animację",
        ['walkingstyles'] = "~h~~p~ Style chodzenia 🚶🏻‍♂️",
        ['resetdef'] = "Zresetuj do ustawień domyślnych",
        ['normalreset'] = "~h~~r~ Normal (Reset)",
        ['moods'] = "~h~~p~ Nastroje 😒",
        ['infoupdate'] = "~h~~g~ Creditsy 🤝🏻",
        ['infoupdateav'] = "Informacje (aktualizacja dostępna)",
        ['infoupdateavtext'] = "Dostępna jest nowa aktualizacja, pobierz ją z: ~y~https://github.com/alberttheprince/rpemotes-reborn~w~",
        ['suggestions'] = "Masz sugestie?",
        ['suggestionsinfo'] = "Napisz do ~r~Noor_Nahas~s~ na forum FiveMa odnośnie przyszłych ficzerów czy propozycji! ✉️",
        ['notvaliddance'] = "nie jest poprawnym tańcem.",
        ['notvalidemote'] = "nie jest poprawną animacją.",
        ['nocancel'] = "Brak animacji do anulowania.",
        ['maleonly'] = "Niestety ta animacja działa tylko dla męskich modeli postaci!",
        ['emotemenucmd'] = "Użyj komendy /emotemenu by otworzyć menu animacji.",
        ['shareemotes'] = "~h~~p~ 👫 Współdzielone animacje",
        ['shareemotesinfo'] = "Zaproś pobliską osobę do wspólnej animacji",
        ['sharedanceemotes'] = "~h~~p~ 🕺 Współdzielone tańce",
        ['notvalidsharedemote'] = "nie jest poprawną współdzieloną animacją.",
        ['sentrequestto'] = "Wysyłasz prośbę do ~y~",
        ['nobodyclose'] = "Nie ma nikogo ~r~w pobliżu~w~.",
        ['doyouwanna'] = "~y~Y~w~ by zaakceptować, ~r~L~w~ by odrzucić (~g~",
        ['refuseemote'] = "Odrzucono Twoją prośbę.",
        ['makenearby'] = "sprawia, że pobliski gracz gra animację",
        ['useleafblower'] = "Naciśnij ~y~G~w~ by użyć dmuchawy do liści.",
        ['camera'] = "Wciśnij ~y~G~w~ by użyć lampy błyskowej aparatu.",
        ['makeitrain'] = "Wciśnij ~y~G~w~ by zrobić deszcz pieniędzy.",
        ['pee'] = "Przytrzymaj ~y~G~w~ by oddać mocz.",
        ['spraychamp'] = "Przytrzymaj ~y~G~w~ by opryskać szampanem",
        ['stun'] = "Wciśnij ~y~G~w~ by 'użyć' tazera.",
        ['smoke'] = "Press ~y~G~w~ to smoke.",
        ['vape'] = "Press ~y~G~w~ to vape.",
        ['candle'] = "press ~y~G~w~ to light candle.",
        ['boundto'] = "Przypisz (~y~%s~w~) do ~g~%s~w~",
        ['currentlyboundemotes'] = " Aktualnie przypisane animacje:",
        ['notvalidkey'] = "nie jest poprawnym klawiszem.",
        ['keybinds'] = "🔢 Przypisane klawisze",
        ['keybindsinfo'] = "Użyj",
        ['searchemotes'] = "~h~~y~ 🔍 Szukaj animacji",
        ['searchinputtitle'] = "Szukaj:",
        ['searchmenudesc'] = "wyników dla frazy",
        ['searchnoresult'] = "Brak wyników dla frazy",
        ['searchshifttofav'] = "Przytrzymaj L-Shift i naciśnij Enter by dodać do ulubionych.",
        ['searchcantsetfav'] = "Współdzielona animacja nie może być dodana do ulubionych.",
        ['invalidvariation'] = "Niepoprawny wariant tekstury. Dostępne tekstury to: %s",
        ['firework'] = "Naciśnij ~y~G~w~ aby odpalić fajerwerki",
        ['poop'] = "Naciśnij ~y~G~w~ by zrobić kupę",
        ['puke'] = "Naciśnij ~y~G~w~, aby zwymiotować", ---- Translated via smodin.io
        ['btn_select'] = "Wybierz",
        ['btn_back'] = "Wstecz",
        ['btn_switch'] = "Ruch",
        ['btn_increment'] = "Increment",
        ['dead'] = "You can't use emotes while dead!",
        ['swimming'] = "You can't use emotes while swimming",
        ['notvalidpet'] = "RUH ROH! Incorrect ped model detected 🐕!",
        ['animaldisabled'] = "Sorry! Animal emotes are disabled on this server",
        ['adultemotedisabled'] = "Bonk! Adult emotes disabled 🔞",
        ['toggle_instructions'] = "Toggle the instructions",
        ['exit_binoculars'] = "Exit binoculars",
        ['toggle_binoculars_vision'] = "Toggle between vision modes",
        ['exit_news'] = "Exit News Camera",
        ['toggle_news_vision'] = "Toggle between vision modes",
        ['edit_values_newscam'] = "Edit the news text",
        ['not_in_a_vehicle'] = "You can't play this animation while in a vehicle",
        ['in_a_vehicle'] = "You can only play this animation while in a vehicle 🚷",
        ['no_anim_crawling'] = "You can't play animations while crawling",
        ['no_anim_right_now'] = "You can't play an animation right now",
    
}
