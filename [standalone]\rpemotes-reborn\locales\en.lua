Locales['en'] = {
    -- English 🇬🇧
    ['emotes'] = 'Emotes 🎬',
    ['danceemotes'] = "🕺 Dance Emotes",
    ['animalemotes'] = "🐩 Animal Emotes",
    ['propemotes'] = "📦 Prop Emotes",
    ['customemotes'] = "⭐ Custom Emotes",
    ['customdanceemotes'] = "⭐ Custom Dance Emotes",
    ['custompropemotes'] = "⭐ Custom Prop Emotes",
    ['customanimalemotes'] = "⭐ Custom Animal Emotes",
    ['favoriteemotes'] = "~h~~y~ 🌟 Favorite",
    ['favoriteinfo'] = "Select an emote here to set it as your favorite.",
    ['rfavorite'] = "Reset favorite",
    ['prop2info'] = "❓ Prop Emotes can be located at the end",
    ['set'] = "Set (",
    ['setboundemote'] = ") to be your bound emote?",
    ['newsetemote'] = "~w~ is now your bound emote, press ~g~CapsLock~w~ to use it.",
    ['cancelemote'] = "Cancel Emote 🚷",
    ['cancelemoteinfo'] = "~r~X~w~ Cancels the currently playing emote",
    ['walkingstyles'] = "Walking Styles 🚶🏻‍♂️",
    ['resetdef'] = "~h~~y~ Reset to default",
    ['normalreset'] = "~h~~r~ Normal (Reset)",
    ['moods'] = "Moods 😒",
    ['infoupdate'] = "Credits 🤝🏻",
    ['remove_emote_keybind'] = 'Delete an emote from keybinds',
    ['show_emote_keybind'] = 'view emotes on keybinds',
    ['play_emote'] = 'play an animation',
    ['open_menu_emote'] = 'Open animation menu',
    ['show_list_emote'] = 'See the list of possible emotes',
    ['link_emote_keybind'] = 'Linking an emote to a key',
    ['help_command'] = 'dance, camera, sit or any other emote',
    ['help_variation'] = '(Optional) 1, 2, 3 or any number. Will change the texture of certain accessories used in emotes, for example the color of a telephone. Enter -1 to see a list of variants',
    ['infoupdateav'] = "Information (Update available)",
    ['infoupdateavtext'] = "An update is available, get the latest version from ~y~https://github.com/alberttheprince/rpemotes-reborn~w~",
    ['suggestions'] = "Suggestions?",
    ['suggestionsinfo'] = "~r~Noor_Nahas~s~ on FiveM forums for any feature/emote suggestions! ✉️",
    ['notvaliddance'] = "is not a valid dance.",
    ['notvalidemote'] = "is not a valid emote.",
    ['nocancel'] = "No emote to cancel.",
    ['maleonly'] = "This emote is male only, sorry!",
    ['emotemenucmd'] = "Use command /emotemenu to open animations menu.",
    ['shareemotes'] = "👫 Shared Emotes",
    ['shareemotesinfo'] = "Invite a nearby person to emote",
    ['sharedanceemotes'] = "🕺 Shared Dances",
    ['notvalidsharedemote'] = "is not a valid shared emote.",
    ['sentrequestto'] = "Sent request to ~y~",
    ['nobodyclose'] = "Nobody ~r~close~w~ enough.",
    ['doyouwanna'] = "~y~Y~w~ to accept, ~r~L~w~ to refuse (~g~",
    ['refuseemote'] = "Emote refused.",
    ['makenearby'] = "makes the nearby player play",
    ['useleafblower'] = "Press ~y~G~w~ to use the leaf blower.",
    ['camera'] = "Press ~y~G~w~ to use camera flash.",
    ['makeitrain'] = "Press ~y~G~w~ to make it rain.",
    ['pee'] = "Hold ~y~G~w~ to pee.",
    ['spraychamp'] = "Hold ~y~G~w~ to spray champagne",
    ['stun'] = "Press ~y~G~w~ to 'use' stun gun.",
    ['smoke'] = "Press ~y~G~w~ to smoke.",
    ['vape'] = "Press ~y~G~w~ to vape.",
    ['candle'] = "press ~y~G~w~ to light candle.",
    ['boundto'] = "Bound (~y~%s~w~) to ~g~%s~w~",
    ['handsup'] = "Hands up",
    ['currentlyboundemotes'] = "Currently bound emotes:",
    ['notvalidkey'] = "is not a valid key.",
    ['keybinds'] = "🔢 Keybinds",
    ['keybindsinfo'] = "Use",
    ['searchemotes'] = "🔍 Search for Emotes",
    ['searchinputtitle'] = "Search:",
    ['searchmenudesc'] = "result(s) for",
    ['searchnoresult'] = "No results for search",
    ['searchshifttofav'] = "Hold L-Shift and press enter to set as favorite.",
    ['searchcantsetfav'] = "Shared emotes cannot be set as favorites.",
    ['invalidvariation'] = "Invalid texture variation. Valid selections are: %s",
    ['firework'] = "Press ~y~G~w~ to use the firework",
    ['poop'] = "Press ~y~G~w~ to poop",
    ['puke'] = "Press ~y~G~w~ to puke",
    ['cut'] = "Press ~y~G~w~ to cut",
    ['btn_select'] = "Select",
    ['btn_back'] = "Back",
    ['btn_increment'] = "Increment",
    ['dead'] = "You can't use emotes while dead!",
    ['swimming'] = "You can't use emotes while swimming",
    ['notvalidpet'] = "RUH ROH! Incorrect ped model detected 🐕!",
    ['animaldisabled'] = "Sorry! Animal emotes are disabled on this server",
    ['adultemotedisabled'] = "Bonk! Adult emotes disabled 🔞",
    ['toggle_instructions'] = "Toggle the instructions",
    ['exit_binoculars'] = "Exit binoculars",
    ['toggle_binoculars_vision'] = "Toggle between vision modes",
    ['exit_news'] = "Exit News Camera",
    ['toggle_news_vision'] = "Toggle between vision modes",
    ['edit_values_newscam'] = "Edit the news text",
    ['not_in_a_vehicle'] = "You can't play this animation while in a vehicle",
    ['in_a_vehicle'] = "You can only play this animation while in a vehicle 🚷",
    ['no_anim_crawling'] = "You can't play animations while crawling",
    ['no_anim_right_now'] = "You can't play an animation right now",
    -- Key maps
    ['register_cancel_emote'] = "Cancel current emote",
    ['register_open_menu'] = "Open animation menu",
    ['register_fav_anim'] = "Play your favorite emote",
    ['register_handsup'] = "Raise hands up",
    ['register_crouch'] = "Crouch",
    ['register_crawl'] = "Crawl",
    ['register_pointing'] = "Point with a finger",
    ['register_ragdoll'] = "Toggle ragdoll",
    -- Commands descriptions
    ['cancel_emote'] = "Cancel current emote",
    ['crouch'] = "Crouch",
    ['crawl'] = "Crawl",
    ['pointing'] = "Finger pointing"
}
