Locales['es'] = {
 -- Spanish 🇪🇸
        ['emotes'] = "~h~~p~ Animaciones 🎬",
        ['danceemotes'] = "~h~~p~ 🕺 Bailes",
        ['animalemotes'] = "~h~~p~ 🐩 Emotes de animales",
        ['propemotes'] = "~h~~p~ 📦 Objetos",
        ['favoriteemotes'] = "~h~~y~ 🌟 Favoritos",
        ['favoriteinfo'] = "Seleccione una animación aquí para configurarlo como su favorito.",
        ['rfavorite'] = "Restablecer favoritos",
        ['prop2info'] = "❓ Los Prop Emotes se pueden encontrar al final",
        ['set'] = "Elegir (",
        ['setboundemote'] = ") como tu animación favorita?",
        ['newsetemote'] = "~w~ es ahora tu animación favorita, presiona ~g~[CapsLock]~w~ para usarla.",
        ['cancelemote'] = "~h~~r~ Cancelar animación 🚷",
        ['cancelemoteinfo'] = "~r~X~w~ Cancela la animación actual.",
        ['walkingstyles'] = "~h~~p~ Formas de caminar 🚶🏻‍♂️",
        ['resetdef'] = "Reiniciar a por defecto",
        ['normalreset'] = "~h~~r~ Normal (Reiniciar)",
        ['moods'] = "~h~~p~ Estados de animo 😒",
        ['infoupdate'] = "~h~~g~ Créditos 🤝🏻",
        ['infoupdateav'] = "Información (Actualización disponible)",
        ['infoupdateavtext'] = "Hay una actualización disponible, para conseguir la ultima version ingrese a ~y~https://github.com/alberttheprince/rpemotes-reborn~w~",
        ['suggestions'] = "Sugerencias?",
        ['suggestionsinfo'] = "~r~Noor_Nahas~s~ en el foro de FiveM para cualquier sugerencia! ✉️",
        ['notvaliddance'] = "no es un baile valido.",
        ['notvalidemote'] = "no es una animación valida.",
        ['nocancel'] = "No hay animación para cancelar.",
        ['maleonly'] = "Esta animación es solo de hombre!",
        ['emotemenucmd'] = "Escribe /emotemenu para abrir el menu.",
        ['shareemotes'] = "~h~~p~ 👫 Animaciones compartidas",
        ['shareemotesinfo'] = "Invita a una persona cercana para la animación.",
        ['sharedanceemotes'] = "~h~~p~ 🕺 Bailes compartidos",
        ['notvalidsharedemote'] = "no es una animación compartida valida.",
        ['sentrequestto'] = "Solicitud enviada ~y~",
        ['nobodyclose'] = "Nadie ~r~cerca~w~.",
        ['doyouwanna'] = "~y~Y~w~ para aceptar, ~r~L~w~ para rechazar (~g~",
        ['refuseemote'] = "Animacion rechazada.",
        ['makenearby'] = "hacer que el jugador cercano juegue",
        ['useleafblower'] = "Presione ~y~G~w~ para utilizar el soplador de hojas.",
        ['camera'] = "Presione ~y~G~w~ para usar el flash de la cámara.",
        ['makeitrain'] = "Presione ~y~G~w~ para hacer llover.",
        ['pee'] = "Mantén la ~y~G~w~ para mear.",
        ['spraychamp'] = "Mantén la ~y~G~w~ para rociar champán.",
        ['smoke'] = "Press ~y~G~w~ to smoke.",
        ['vape'] = "Presione ~y~G~w~ para vapear.",
        ['candle'] = "press ~y~G~w~ to light candle.",
        ['boundto'] = "Unida (%s) a %s", 
        ['currentlyboundemotes'] = " Emotes vinculados actualmente:",
        ['notvalidkey'] = "no es una clave válida.",
        ['keybinds'] = "🔢 Keybinds",
        ['keybindsinfo'] = "Utilizar",
        ['searchemotes'] = "~h~~y~ 🔍 Busca animaciones",
        ['searchinputtitle'] = "Buscar:",
        ['searchmenudesc'] = "resultado(s) para",
        ['searchnoresult'] = "No se encontró nada con",
        ['searchshifttofav'] = "Mantén L-Shift y presiona Enter para guardar como favorito.",
        ['searchcantsetfav'] = "Las animaciones compartidas no pueden ser guardadas como favoritas.",
        ['invalidvariation'] = "Variación de textura no válida. Las opciones válidas son: %s",
        ['firework'] = "Presione ~y~G~w~ para usar los fuegos artificiales",
        ['poop'] = "Presione ~y~G~w~ para hacer caca",
        ['puke'] = "Presiona ~y~G~w~ para vomitar", ---- Translated via smodin.io
	['cut'] = "Press ~y~G~w~ to cut",
        ['btn_select'] = "Seleccionar",
        ['btn_back'] = "Atrás ",
        ['btn_switch'] = "Movimiento ",
        ['btn_increment'] = "Increment",
        ['dead'] = "¡No puedes usar animaciones mientras estás muerto!",
        ['swimming'] = "You can't use emotes while swimming",
        ['notvalidpet'] = "RUH ROH! Incorrect ped model detected 🐕!",
        ['animaldisabled'] = "Sorry! Animal emotes are disabled on this server",
        ['adultemotedisabled'] = "Bonk! Adult emotes disabled 🔞",
        ['toggle_instructions'] = "Toggle the instructions",
        ['exit_binoculars'] = "Exit binoculars",
        ['toggle_binoculars_vision'] = "Toggle between vision modes",
        ['exit_news'] = "Exit News Camera",
        ['toggle_news_vision'] = "Toggle between vision modes",
        ['edit_values_newscam'] = "Edit the news text",
        ['not_in_a_vehicle'] = "You can't play this animation while in a vehicle",
        ['in_a_vehicle'] = "You can only play this animation while in a vehicle 🚷",
        ['no_anim_crawling'] = "You can't play animations while crawling",
        ['no_anim_right_now'] = "You can't play an animation right now",
    
}
