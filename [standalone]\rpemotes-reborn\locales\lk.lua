Locales['lk'] = {
 -- Sinhala 🇱🇰
        ['emotes'] = "~h~~p~ Emotes 🎬",
        ['danceemotes'] = "~h~~p~ 🕺 Natana Emotes",
        ['animalemotes'] = "~h~~p~ 🐩 Sattunge Emotes",
        ['propemotes'] = "~h~~p~ 📦 Prop Emotes",
        ['favoriteemotes'] = "~h~~y~ 🌟 Favorite Emotes",
        ['favoriteinfo'] = "Methanin oyage favorite emote eka thoraganna.",
        ['rfavorite'] = "Favorite emote eka reset karanna",
        ['prop2info'] = "❓ Prop emotes tiyenne anthimata",
        ['set'] = "Set karanna (",
        ['setboundemote'] = ") favorite emote eka widiyata?",
        ['newsetemote'] = "~w~ thamai oyage favorite emote eka, ~g~CapsLock~w~ eken use karanna.",
        ['cancelemote'] = "~h~~r~ Emote Eka Nawattanna 🚷",
        ['cancelemoteinfo'] = "~r~X~w~ <PERSON><PERSON> thiyana emote eka nawattanawa",
        ['walkingstyles'] = "~h~~p~ <PERSON>widina Styles 🚶🏻‍♂️",
        ['resetdef'] = "Yathã thatweta path karanna",
        ['normalreset'] = "~h~~r~ Normal (Reset karanna)",
        ['moods'] = "~h~~p~ Mood Eka 😒",
        ['infoupdate'] = "~h~~g~ Credits 🤝🏻",
        ['infoupdateav'] = "Information (Update ekak tiyanawa)",
        ['infoupdateavtext'] = "Update ekak tiyanawa, alutma update eka methanin ganna ~y~https://github.com/alberttheprince/rpemotes-reborn~w~",
        ['suggestions'] = "Suggestions?",
        ['suggestionsinfo'] = "~r~Noor_Nahas~s~ on FiveM forums for any feature/emote suggestions! ✉️",
        ['notvaliddance'] = "kiyanne weradi natana emote ekak.",
        ['notvalidemote'] = "kiyanne weradi emote ekak.",
        ['nocancel'] = "Nawattanna emote ekak na.",
        ['maleonly'] = "Me emote eka piriminta witharai, sorry!",
        ['emotemenucmd'] = "/emotemenu kiyana command eken animations menu eka ganna.",
        ['shareemotes'] = "~h~~p~ 👫 Shared Emotes",
        ['shareemotesinfo'] = "Laga inna kenata emote eka danna katha krnna",
        ['sharedanceemotes'] = "~h~~p~ 🕺 Shared Natana Emotes",
        ['notvalidsharedemote'] = "kiyanne weradi shared emote ekak.",
        ['sentrequestto'] = "Request ekak yewwa ~y~",
        ['nobodyclose'] = "Kawruwat ~r~Laga~w~ na.",
        ['doyouwanna'] = "~y~Y~w~ accept karanna, ~r~L~w~ decline karanna (~g~",
        ['refuseemote'] = "Emote eka decline kara.",
        ['makenearby'] = "laga inna playerta play karawanna",
        ['useleafblower'] = "Meka weda karawanna ~y~G~w~ press karanna.",
        ['camera'] = "Camera flash ekata ~y~G~w~ press karanna.",
        ['makeitrain'] = "Salli wattanna ~y~G~w~ press karanna.",
        ['pee'] = "Chuu danna ~y~G~w~ obagena inna.",
        ['spraychamp'] = "Spray karanna ~y~G~w~ press karanna",
        ['stun'] = "Stun karanna ~y~G~w~ press karanna.",
        ['smoke'] = "Press ~y~G~w~ to smoke.",
        ['vape'] = "Vape ekata ~y~G~w~ press karanna.",
        ['candle'] = "press ~y~G~w~ to light candle.",
        ['boundto'] = "Bound karanna (~y~%s~w~) ta ~g~%s~w~",
        ['currentlyboundemotes'] = " Denata bound karapu emotes:",
        ['notvalidkey'] = "kiyanne weradi key ekak.",
        ['keybinds'] = "🔢 Keybinds",
        ['keybindsinfo'] = "Use karanna",
        ['searchemotes'] = "~h~~y~ 🔍 Emotes Hoyaganna",
        ['searchinputtitle'] = "Search karanna:",
        ['searchmenudesc'] = "result(s) for",
        ['searchnoresult'] = "Mukut hambune na",
        ['searchshifttofav'] = "L-Shift obagena Enter press karala, favorite karaganna.",
        ['searchcantsetfav'] = "Shared emotes favorite karanna ba.",
        ['invalidvariation'] = "Weradi texture variation ekak. Hari ewa thamai: %s",
        ['firework'] = "Firework ekata ~y~G~w~ press karanna",
        ['poop'] = "Kakki danna ~y~G~w~ press karanna",
        ['puke'] = "Wamane danna ~y~G~w~ press karanna",
	['cut'] = "Press ~y~G~w~ to cut",
        ['btn_select'] = "Select Karanna",
        ['btn_back'] = "Aapassata",
        ['btn_switch'] = "Movement eka",
        ['btn_increment'] = "Wedi karanna",
        ['dead'] = "You can't use emotes while dead!",
        ['swimming'] = "You can't use emotes while swimming",
        ['notvalidpet'] = "RUH ROH! Incorrect ped model detected 🐕!",
        ['animaldisabled'] = "Sorry! Animal emotes are disabled on this server",
        ['adultemotedisabled'] = "Bonk! Adult emotes disabled 🔞",
        ['toggle_instructions'] = "Toggle the instructions",
        ['exit_binoculars'] = "Exit binoculars",
        ['toggle_binoculars_vision'] = "Toggle between vision modes",
        ['exit_news'] = "Exit News Camera",
        ['toggle_news_vision'] = "Toggle between vision modes",
        ['edit_values_newscam'] = "Edit the news text",
        ['not_in_a_vehicle'] = "You can't play this animation while in a vehicle",
        ['in_a_vehicle'] = "You can only play this animation while in a vehicle 🚷",
        ['no_anim_crawling'] = "You can't play animations while crawling",
        ['no_anim_right_now'] = "You can't play an animation right now",
    
}
