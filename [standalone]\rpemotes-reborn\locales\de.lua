Locales['de'] = {
        -- German 🇩🇪
        ['emotes'] = "~h~~p~ Emotes 🎬",
        ['danceemotes'] = "~h~~p~ 🕺 Tanz-Emotes",
        ['animalemotes'] = "~h~~p~ 🐩 Tier Emotes",
        ['propemotes'] = "~h~~p~ 📦 Prop-Emotes",
        ['favoriteemotes'] = "~h~~y~ 🌟 Favoriten",
        ['favoriteinfo'] = "Wähle hier ein Emote, um es als Favorit festzulegen.",
        ['rfavorite'] = "Favorit zurücksetzen",
        ['prop2info'] = "❓ Prop-Emotes können am Ende platziert werden",
        ['set'] = "Setze (",
        ['setboundemote'] = ") soll dein gebundenes Emote sein?",
        ['newsetemote'] = "~w~ ist jetzt ein gebundenes Emote, drücke ~g~CapsLock~w~, um es zu verwenden.",
        ['cancelemote'] = "~h~~r~ Emote abbrechen 🚷",
        ['cancelemoteinfo'] = "~r~ X ~w~ Bricht das aktuell wiedergegebene Emote ab",
        ['walkingstyles'] = "~h~~p~ Gehstile 🚶🏻‍♂️",
        ['resetdef'] = "~h~~y~ Auf Standard zurücksetzen",
        ['normalreset'] = "~h~~r~ Normal (Zurücksetzen)",
        ['moods'] = "~h~~p~ Stimmungen 😒",
        ['infoupdate'] = "~h~~g~ Credits 🤝🏻",
        ['remove_emote_keybind'] = 'Emote von den Tastenkombinationen entfernen',
        ['show_emote_keybind'] = 'Emotes mit Tastenkombination anzeigen',
        ['play_emote'] = 'Eine Animation abspielen',
        ['open_menu_emote'] = 'Animationsmenü öffnen',
        ['show_list_emote'] = 'Liste der möglichen Emotes anzeigen',
        ['link_emote_keybind'] = 'Einen Emote einer Taste zuordnen',
        ['help_command'] = 'Tanz, Kamera, Sitz oder andere Emotes',
        ['help_variation'] = '(Optional) 1, 2, 3 oder eine andere Zahl. Ändert die Textur bestimmter Accessoires in Emotes, z.B. die Farbe eines Telefons. Gib -1 ein, um eine Liste der Varianten zu sehen',
        ['infoupdateav'] = "Information (Update verfügbar)",
        ['infoupdateavtext'] = "Eine Aktualisierung ist verfügbar ~y~https://github.com/alberttheprince/rpemotes-reborn~w~",
        ['suggestions'] = "Vorschläge?",
        ['suggestionsinfo'] = "~r~Noor_Nahas~s~ in FiveM-Foren für alle Feature- / Emote-Vorschläge! ✉️",
        ['notvaliddance'] = "ist kein gültiger Tanz",
        ['notvalidemote'] = "ist kein gültiges Emote",
        ['nocancel'] = "Kein Emote zum Abbrechen",
        ['maleonly'] = "Dieser Emote ist nur für Männer, tut mir leid!",
        ['emotemenucmd'] = "Verwende den Befehl /emotemenu, um das Animationsmenü zu öffnen.",
        ['shareemotes'] = "~h~~p~ 👫 Geteilte Emotes",
        ['shareemotesinfo'] = "Laden Sie eine Person in Ihrer Nähe zum Emoten ein",
        ['sharedanceemotes'] = "~h~~p~ 🕺 Geteilte Tänze",
        ['notvalidsharedemote'] = "ist kein gültiges geteiltes Emote.",
        ['sentrequestto'] = "Anfrage an ~g~ gesendet",
        ['nobodyclose'] = "Niemand ist nah genug dran.",
        ['doyouwanna'] = "~y~Z~w~ zu akzeptieren, ~r~L~w~ zu verweigern (~g~",
        ['refuseemote'] = "Emote abgelehnt.",
        ['makenearby'] = "Starte einen Emote mit einer Person in deiner Nähe",
        ['useleafblower'] = "Drücke ~y~G~w~, um den Laubgebläse zu verwenden.",
        ['camera'] = "Drücke ~y~G~w~, um den Kamera-Blitz zu verwenden.",
        ['makeitrain'] = "Drücke ~y~G~w~, um Geld zu werfen.",
        ['pee'] = "Halte ~y~G~w~, um zu urinieren.",
        ['spraychamp'] = "Halte ~y~G~w~, um Champagner zu sprühen",
        ['stun'] = "Drücke ~y~G~w~, um die Elektroschockpistole zu 'verwenden'.",
        ['smoke'] = "Halte ~y~G~w~, um zu rauchen.",
        ['vape'] = "Halte ~y~G~w~, um zu vapen.",
        ['candle'] = "Drücke ~y~G~w~, um die Kerze anzuzünden.",
        ['boundto'] = "Gebunden (~y~%s~w~) an ~g~%s~w~",
        ['handsup'] = "Hände hoch",
        ['currentlyboundemotes'] = "Aktuell gebundene Emotes:",
        ['notvalidkey'] = "ist keine gültige Taste.",
        ['keybinds'] = "🔢 Tastenkombinationen",
        ['keybindsinfo'] = "verwenden",
        ['searchemotes'] = "~h~~y~ 🔍 Suche nach Bestimmten Emotes",
        ['searchinputtitle'] = "Suche:",
        ['searchmenudesc'] = "Ergebnis(se) für ",
        ['searchnoresult'] = "Es wurden keine Ergebnisse gefunden für",
        ['searchshifttofav'] = "Halte L-Shift und drücke Enter, um das Emote als Favorit festzulegen.",
        ['searchcantsetfav'] = "Geteilte Emotes können nicht als Favorit gesetzt werden.",
        ['invalidvariation'] = "Ungültige Texturvariante. Gültige Auswahlen sind: %s",
        ['firework'] = "Drücke ~y~G~w~, um das Feuerwerk zu zünden",
        ['poop'] = "Drücke ~y~G~w~, um zu kacken",
        ['puke'] = "Drücke ~y~G~w~, um dich zu übergeben",
        ['cut'] = "Drücke ~y~G~w~, um zu schneiden",
        ['btn_select'] = "Auswählen",
        ['btn_back'] = "Zurück",
        ['btn_increment'] = "Erhöhen",
        ['dead'] = "Du kannst keine Emotes verwenden, während du tot bist!",
        ['swimming'] = "Du kannst keine Emotes verwenden, während du schwimmst",
        ['notvalidpet'] = "RUH ROH! Falsches Tiermodell erkannt 🐕!",
        ['animaldisabled'] = "Entschuldigung! Tier-Emotes sind auf diesem Server deaktiviert",
        ['adultemotedisabled'] = "Bonk! Erwachsene Emotes deaktiviert 🔞",
        ['toggle_instructions'] = "Anweisungen umschalten",
        ['exit_binoculars'] = "Fernglas verlassen",
        ['toggle_binoculars_vision'] = "Zwischen den Ansichtsmodi wechseln",
        ['exit_news'] = "Nachrichtenkamera verlassen",
        ['toggle_news_vision'] = "Zwischen den Ansichtsmodi wechseln",
        ['edit_values_newscam'] = "Den Nachrichtentext bearbeiten",
        ['not_in_a_vehicle'] = "Du kannst diese Animation nicht im Fahrzeug abspielen",
        ['in_a_vehicle'] = "Du kannst diese Animation nur im Fahrzeug abspielen 🚷",
        ['no_anim_crawling'] = "Du kannst keine Animationen abspielen, während du kriechst",
        ['no_anim_right_now'] = "Du kannst jetzt keine Animation abspielen",
        -- Key maps
        ['register_cancel_emote'] = "Aktuellen Emote abbrechen",
        ['register_open_menu'] = "Animationsmenü öffnen",
        ['register_fav_anim'] = "Deinen Favoriten-Emote abspielen",
        ['register_handsup'] = "Hände hoch",
        ['register_crouch'] = "Hocken",
        ['register_crawl'] = "Kriechen",
        ['register_pointing'] = "Mit dem Finger zeigen",
        ['register_ragdoll'] = "Ragdoll umschalten",
        -- Commands descriptions
        ['cancel_emote'] = "Aktuellen Emote abbrechen",
        ['crouch'] = "Hocken",
        ['crawl'] = "Kriechen",
        ['pointing'] = "Mit dem Finger zeigen"
    }
    