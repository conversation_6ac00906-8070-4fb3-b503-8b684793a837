Locales['hu'] = {
 -- Hungarian 🇭🇺
        ['emotes'] = "~h~~p~ <PERSON>im<PERSON>ciós Menü 🎬",
        ['danceemotes'] = "~h~~p~ 🕺 Tánc Animációk",
        ['animalemotes'] = "~h~~p~ 🐩 Állatos Animációk",
        ['propemotes'] = "~h~~p~ 📦 T<PERSON>im<PERSON>",
        ['favoriteemotes'] = "~h~~y~ 🌟 Kedvenc",
        ['favoriteinfo'] = "Válassz ki itt egy animációt, hogy kedvenceddé tedd.",
        ['rfavorite'] = "Kedvenc alaphelyzetbe állítása",
        ['prop2info'] = "❓ A Prop Emote-ok a végén találhatók",
        ['set'] = "Kiválasztod (",
        ['setboundemote'] = ") hogy ez legyen a kedvenc animációd?",
        ['newsetemote'] = "~w~ lett a kedvenc animációd, nyomj egy ~g~CapsLock~w~-ot a használatához.",
        ['cancelemote'] = "~h~~r~ <PERSON>im<PERSON><PERSON>ó Befejezése 🚷",
        ['cancelemoteinfo'] = "~r~X~w~ Megszünteti az aktuálisan lejátszott animációt",
        ['walkingstyles'] = "~h~~p~ Séta Stílusok 🚶🏻‍♂️",
        ['resetdef'] = "Séta alaphelyzetbe állítása",
        ['normalreset'] = "~h~~r~ Normális (Alaphelyzet)",
        ['moods'] = "~h~~p~ Archangulatok 😒",
        ['infoupdate'] = "~h~~g~ Kreditek 🤝🏻",
        ['infoupdateav'] = "Információ (frissítés elérhető)",
        ['infoupdateavtext'] = "A frissítés elérhető, a legfrissebb verziót a következő címen tudod beszerezni ~y~https://github.com/alberttheprince/rpemotes-reborn~w~",
        ['suggestions'] = "Javaslatok?",
        ['suggestionsinfo'] = "~r~Noor_Nahas~s~ a FiveM fórumokon bármilyen funkcióra/animációra vonatkozó javaslatért! ✉️",
        ['notvaliddance'] = "nem érvényes tánc.",
        ['notvalidemote'] = "nem érvényes animáció.",
        ['nocancel'] = "Nincs animáció amit visszavonhatnál.",
        ['maleonly'] = "Ez az animáció csak férfi karaktereknek szól, sajnálom!",
        ['emotemenucmd'] = "A /emotemenu paranccsal nyisd meg az animációk menüt.",
        ['shareemotes'] = "~h~~p~ 👫 Megosztott animációk",
        ['shareemotesinfo'] = "Hívj meg egy közeli személyt animációra",
        ['sharedanceemotes'] = "~h~~p~ 🕺 Közös Táncok",
        ['notvalidsharedemote'] = "nem érvényes megosztott animáció.",
        ['sentrequestto'] = "Elküldted a kérelmet a(z) ~y~",
        ['nobodyclose'] = "Senki sincs elég ~r~közel~w~.",
        ['doyouwanna'] = "~y~Y~w~ gomb az elfogadáshoz, ~r~L~w~ gomb az elutasításhoz (~g~",
        ['refuseemote'] = "Animáció elutasítva.",
        ['makenearby'] = "a közelben lévő játékos játsza le ezt az animációt:",
        ['useleafblower'] = "Nyomj ~y~G~w~ gombot a lombfúvó használatához.",
        ['camera'] = "Nyomj ~y~G~w~ gombot a fényképezőgép vakujának használatához.",
        ['makeitrain'] = "Nyomj ~y~G~w~ gombot hogy elkezdd szórni a pénzt.",
        ['pee'] = "Nyomj ~y~G~w~ gombot a pisiléshez.",
        ['spraychamp'] = "Nyomj ~y~G~w~ gombot a pezsgő fröcsköléséhez.",
        ['stun'] = "Nyomj ~y~G~w~ gombot hogy 'használd' a sokkolót.",
        ['smoke'] = "Press ~y~G~w~ to smoke.",
        ['vape'] = "Nyomj ~y~G~w~ gombot a vapeeléshez.",
        ['candle'] = "press ~y~G~w~ to light candle.",
        ['boundto'] = "Rögzített (%s) ehhez %s",
        ['currentlyboundemotes'] = " Jelenleg rögzített animációk:",
        ['notvalidkey'] = "nem érvényes gombkiosztás.",
        ['keybinds'] = "🔢 Gombkiosztások",
        ['keybindsinfo'] = "Használat",
        ['searchemotes'] = "~h~~y~ 🔍 Animációk Keresése",
        ['searchinputtitle'] = "Keresés:",
        ['searchmenudesc'] = "eredmény erre: ",
        ['searchnoresult'] = "Nincs eredmény erre:",
        ['searchshifttofav'] = "Tartsd lenyomva az L-Shift billentyűt, és nyomd meg az enter billentyűt a kedvencek beállításához.",
        ['searchcantsetfav'] = "A megosztott animációkat nem lehet kedvencekként beállítani.",
        ['invalidvariation'] = "Érvénytelen textúra variáció. Érvényes választások a következők: %s",
        ['firework'] = "Nyomj ~y~G~w~ gombot a tűzijáték használatához",
        ['poop'] = "Nyomj ~y~G~w~ gombot a kakiláshoz",
        ['puke'] = "Nyomja meg az ~y~G~w~ gombot a hányáshoz", ---- Translated via smodin.io
	['cut'] = "Press ~y~G~w~ to cut",
        ['btn_select'] = "Kiválasztás",
        ['btn_back'] = "Vissza",
        ['btn_switch'] = "Mozgás",
        ['btn_increment'] = "Increment",
        ['swimming'] = "You can't use emotes while swimming",
        ['notvalidpet'] = "RUH ROH! Incorrect ped model detected 🐕!",
        ['animaldisabled'] = "Sorry! Animal emotes are disabled on this server",
        ['adultemotedisabled'] = "Bonk! Adult emotes disabled 🔞",
        ['toggle_instructions'] = "Toggle the instructions",
        ['exit_binoculars'] = "Exit binoculars",
        ['toggle_binoculars_vision'] = "Toggle between vision modes",
        ['exit_news'] = "Exit News Camera",
        ['toggle_news_vision'] = "Toggle between vision modes",
        ['edit_values_newscam'] = "Edit the news text",
        ['not_in_a_vehicle'] = "You can't play this animation while in a vehicle",
        ['in_a_vehicle'] = "You can only play this animation while in a vehicle 🚷",
        ['no_anim_crawling'] = "You can't play animations while crawling",
        ['no_anim_right_now'] = "You can't play an animation right now",
    
}
