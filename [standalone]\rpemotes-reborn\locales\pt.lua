Locales['pt'] = {
 -- Brazilian Portuguese 🇧🇷
        ['emotes'] = "~h~~p~ Emotes 🎬",
        ['danceemotes'] = "~h~~p~ 🕺 Emotes de Danças",
        ['animalemotes'] = "~h~~p~ 🐩 Emotes de Animais",
        ['propemotes'] = "~h~~p~ 📦 Emotes com Props",
        ['favoriteemotes'] = "~h~~y~ 🌟 Favoritos",
        ['favoriteinfo'] = "Selecione um emote para colocá-lo nos seus favoritos",
        ['rfavorite'] = "Limpar favoritos",
        ['prop2info'] = "❓ Emotes de props podem ser localizados no fim",
        ['set'] = "Set (",
        ['setboundemote'] = ") para ser seu emote vinculado?",
        ['newsetemote'] = "~w~ é o seu emote vinculado, pressione ~g~CapsLock~w~ para usá-lo",
        ['cancelemote'] = "~h~~r~ Cancelar emote 🚷",
        ['cancelemoteinfo'] = "~r~X~w~ Cancela os emotes rodando atualmente",
        ['walkingstyles'] = "~h~~p~ Estilos de Caminhada 🚶🏻‍♂️",
        ['resetdef'] = "Resetar para o padrão",
        ['normalreset'] = "~h~~r~ Normal (Resetar)",
        ['moods'] = "~h~~p~ Humores 😒",
        ['infoupdate'] = "~h~~y~ Crédito 🤝🏻",
        ['infoupdateav'] = "Informação (Atualização disponível)",
        ['infoupdateavtext'] = "Uma atualização disponível, veja ~y~https://github.com/alberttheprince/rpemotes-reborn~w~ para pegar",
        ['suggestions'] = "Sugestões?",
        ['suggestionsinfo'] = "~r~Noor_Nahas~s~ no fórum do FiveM para qualquer sugestão de recurso/emotes! ✉️",
        ['notvaliddance'] = "não é uma dança válida.",
        ['notvalidemote'] = "não é um emote válido.",
        ['nocancel'] = "Nenhum emote para cancelar",
        ['maleonly'] = "Este emote é para homens, desculpe!",
        ['emotemenucmd'] = "Use /emotemenu para abrir o menu.",
        ['shareemotes'] = "~h~~p~ 👫 Emotes compartilhados",
        ['shareemotesinfo'] = "Convide uma pessoa próxima para realizar a animação",
        ['sharedanceemotes'] = "~h~~p~ 🕺 Danças compartilhadas",
        ['notvalidsharedemote'] = "não é um emote compartilhado válido.",
        ['sentrequestto'] = "Enviar solicitação para ~y~",
        ['nobodyclose'] = "Ninguém próximo o ~r~suficiente~w~.",
        ['doyouwanna'] = "~y~Y~w~ para aceitar, ~r~L~w~ para recusar (~g~",
        ['refuseemote'] = "Emote recusado",
        ['makenearby'] = "Faz o jogador próximo participar",
        ['useleafblower'] = "Pressione ~y~G~w~ para usar o soprador de folhas",
        ['camera'] = "Pressione ~y~G~w~ para usar o flash da câmera",
        ['makeitrain'] = "Pressione ~y~G~w~ para fazer chover.",
        ['pee'] = "Mantenha pressionado ~y~G~w~ para fazer xixi.",
        ['spraychamp'] = "Mantenha  pressionado ~y~G~w~ jogar champagne",
        ['stun'] = "Pressione ~y~G~w~ para 'usar' stun gun.",
        ['smoke'] = "Press ~y~G~w~ to smoke.",
        ['vape'] = "Pressione ~y~G~w~ para vape.",
        ['candle'] = "press ~y~G~w~ to light candle.",
        ['boundto'] = "Vinculado (~y~%s~w~) para ~g~%s~w~",
        ['currentlyboundemotes'] = "Emotes atualmente vinculados: ",
        ['notvalidkey'] = "isto não é uma chave válida",
        ['keybinds'] = "🔢 Keybinds",
        ['keybindsinfo'] = "Usar",
        ['searchemotes'] = "~h~~y~ 🔍 Procure por Emotes",
        ['searchinputtitle'] = "Procurar:",
        ['searchmenudesc'] = "resultado(s) para",
        ['searchnoresult'] = "Nenhum resultado para a pesquisa",
        ['searchshifttofav'] = "Segure Shift Esquerdo e pressione enter para setar como favorito.",
        ['searchcantsetfav'] = "Emotes compartilhados não podem ser setados como favorito.",
        ['invalidvariation'] = "Variação de textura inválida. As opções válidas são: %s",
        ['firework'] = "Pressione ~y~G~w~ para usar o fogo de artifício",
        ['poop'] = "Pressione ~y~G~w~ para fazer cocô", -- Translated using smodin.io
        ['puke'] = "Pressione ~y~G~w~ para vomitar",
	['cut'] = "Press ~y~G~w~ to cut",
        ['btn_select'] = "Selecionar",
        ['btn_back'] = "Voltar",
        ['btn_switch'] = "Movimento",
        ['btn_increment'] = "Incremento",
        ['dead'] = "You can't use emotes while dead!",
        ['swimming'] = "You can't use emotes while swimming",
        ['notvalidpet'] = "RUH ROH! Incorrect ped model detected 🐕!",
        ['animaldisabled'] = "Sorry! Animal emotes are disabled on this server",
        ['adultemotedisabled'] = "Bonk! Adult emotes disabled 🔞",
        ['toggle_instructions'] = "Toggle the instructions",
        ['exit_binoculars'] = "Exit binoculars",
        ['toggle_binoculars_vision'] = "Toggle between vision modes",
        ['exit_news'] = "Exit News Camera",
        ['toggle_news_vision'] = "Toggle between vision modes",
        ['edit_values_newscam'] = "Edit the news text",
        ['not_in_a_vehicle'] = "You can't play this animation while in a vehicle",
        ['in_a_vehicle'] = "You can only play this animation while in a vehicle 🚷",
        ['no_anim_crawling'] = "You can't play animations while crawling",
        ['no_anim_right_now'] = "You can't play an animation right now",
    
}
