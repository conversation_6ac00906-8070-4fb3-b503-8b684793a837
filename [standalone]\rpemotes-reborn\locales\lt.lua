Locales['lt'] = {
    -- Lithuanian (LT)
    ['emotes'] = '~h~~p~ Animacijos 🎬',
    ['danceemotes'] = "~h~~p~ 🕺 Sokiu animacijos",
    ['animalemotes'] = "~h~~p~ 🐩 Gyvunu animacijos",
    ['propemotes'] = "~h~~p~ 📦 Daiktu animacijos",
    ['favoriteemotes'] = "~h~~y~ 🌟 Megstamiausi",
    ['favoriteinfo'] = "Cia pasirinkite jaustuka, kad nustatytumete ji kaip megstamiausia.",
    ['rfavorite'] = "Atstatyti megstamiausia",
    ['prop2info'] = "❓ Daiktu animacijos gali buti pabaigoje",
    ['set'] = "Rinkinys (",
    ['setboundemote'] = ") buti tavo surista emocija?",
    ['newsetemote'] = "~w~ dabar yra jusu susietas jaustukas, paspauskite ~g~CapsLock~w~, kad ji naudotumete.",
    ['cancelemote'] = "~h~~r~ Atsaukti animacija 🚷",
    ['cancelemoteinfo'] = "~r~X~w~ Atsaukti dabar naudojama animacija",
    ['walkingstyles'] = "~h~~p~ Ejimo stiliai 🚶🏻‍♂️",
    ['resetdef'] = "~h~~y~ Atstatyti i numatytuosius nustatymus",
    ['normalreset'] = "~h~~r~ Iprasta (nustatyti is naujo)",
    ['moods'] = "~h~~p~ Nuotaikos 😒",
    ['emotemenu'] = 'Žr. galimų emocijų sąrašą',
    ['remove_emote_keybind'] = 'Ištrinti animaciją iš susieto mygtuko',
    ['show_emote_keybind'] = 'peržiūrėti animacijas ant klavišų',
    ['play_emote'] = 'vykdyti animaciją',
    ['open_menu_emote'] = 'Animacijos',
    ['show_list_emote'] = 'Peržiūrėti galimų emocijų sąrašą',
    ['link_emote_keybind'] = 'Animacijos susiejimas su mygtuku',
    ['help_command'] = 'dance, camera, sit arba kita animacija',
    ['help_variation'] = '(Nebūtina) 1, 2, 3 ar kitas skaičius. Pakeis tam tikrų priedų, naudojamų emocijoms, tekstūrą, pavyzdžiui, telefono spalvą. Įrašykite -1 norėdami pamatyti variantų sąrašą',
    ['infoupdate'] = "~h~~g~ Porines animacijos 🤝🏻",
    ['infoupdateavtext'] = "Galimas naujinimas, gaukite naujausia versija is ~y~https://github.com/alberttheprince/rpemotes-reborn~w~",
    ['suggestions'] = "Pasiulymus?",
    ['suggestionsinfo'] = "~r~Noor_Nahas~s~ FiveM forumuose del bet kokiu funkciju / emociju pasiulymu! ✉️",
    ['notvaliddance'] = "Sokis nera tinkamas.",
    ['notvalidemote'] = "Nera tinkama animacija.",
    ['nocancel'] = "Nera animaciju, kurias butu galima atsaukti.",
    ['maleonly'] = "Si animacija yra tik vyriska, atsiprasome!",
    ['emotemenucmd'] = "Noredami atidaryti animaciju meniu, naudokite komanda /emotemenu.",
    ['shareemotes'] = "~h~~p~ 👫 Porines animacijos",
    ['shareemotesinfo'] = "Pakvieskite salia esanti zmogu animacijai kartu",
    ['sharedanceemotes'] = "~h~~p~ 🕺 Poriniai sokiai",
    ['notvalidsharedemote'] = "Nera tinkama porine animacija.",
    ['sentrequestto'] = "Prasymas issiustas asmeniui ~y~",
    ['nobodyclose'] = "Nieko ~r~arti~w~ nera.",
    ['doyouwanna'] = "~y~Y~w~ priimti, ~r~L~w~ atsisakyti (~g~",
    ['refuseemote'] = "Animacijos atsisake.",
    ['makenearby'] = "Kviecia arti esanti asmeni bendrai animacijai",
    ['useleafblower'] = "Paspauskite ~y~G~w~, kad naudotumete lapu pustuva.",
    ['camera'] = "Paspauskite ~y~G~w~, kad galetumete naudoti fotoaparato blykste.",
    ['makeitrain'] = "Paspauskite ~y~G~w~, kad pradetu lyti.",
    ['pee'] = "Laikykites ~y~G~w~, kad slapintumete.",
    ['spraychamp'] = "Laikykite ~y~G~w~, kad ispurkstumete sampana",
    ['stun'] = "Paspauskite ~y~G~w~, kad 'naudotumete' apsvaiginimo pistoleta.",
    ['smoke'] = "Press ~y~G~w~ to smoke.",
    ['vape'] = "Paspauskite ~y~G~w~, kad garuotumete",
    ['candle'] = "press ~y~G~w~ to light candle.",
    ['boundto'] = "Suristi (~y~%s~w~) I ~g~%s~w~",
    ['handsup'] = "pakelti rankas",
    ['currentlyboundemotes'] = " Siuo metu susietos animacijos:",
    ['notvalidkey'] = "raktas nera tinkamas.",
    ['keybinds'] = "🔢 Keybind'ai",
    ['keybindsinfo'] = "Naudoti",
    ['searchemotes'] = "~h~~y~ 🔍 Iskoti animacijos",
    ['searchinputtitle'] = "Ieskoti:",
    ['searchmenudesc'] = "rezultatas (-ai), skirtas",
    ['searchnoresult'] = "Paieskos rezultatu nera",
    ['searchshifttofav'] = "Laikykite nuspaude L-Shift ir paspauskite Enter, kad nustatytumete kaip megstamiausia.",
    ['searchcantsetfav'] = "Bendrinamos animacijos negali buti nustatytos kaip megstamiausios.",
    ['invalidvariation'] = "Netinkamas teksturos variantas. Galiojantys pasirinkimai yra: %s",
    ['firework'] = "Paspauskite ~y~G~w~, kad galetumete naudoti fejerverka",
    ['poop'] = "Paspauskite ~y~G~w~, kad istustumete",
    ['puke'] = "Paspauskite ~y~G~w~, kad vemtumete",
    ['cut'] = "Press ~y~G~w~ to cut",
    ['btn_select'] = "Pasirinkti",
    ['btn_back'] = "Atgal",
    ['btn_switch'] = "Judejimas",
    ['btn_increment'] = "Padidejimas",
    ['dead'] = "Negalite naudoti animaciju, kai esate mires!",
    ['swimming'] = "Jus negalite naudoti animaciju plaukdami",
    ['notvalidpet'] = "AU AU! Aptiktas netinkamas ped modelis 🐕!",
    ['animaldisabled'] = "atsiprasome! Gyvunu emocijos siame serveryje isjungtos",
    ['adultemotedisabled'] = "Boom! Suaugusiuju emocijos neleidziamos 🔞",
    ['toggle_instructions'] = "Perjunkite nurodymus",
    ['exit_binoculars'] = "Pasidekite ziuronus",
    ['toggle_binoculars_vision'] = "Perjungti regejimo rezimus",
    ['exit_news'] = "Iseikite is 'News Camera'.",
    ['toggle_news_vision'] = "Perjungti regejimo rezimus",
    ['edit_values_newscam'] = "Redaguoti naujienu teksta",
    ['not_in_a_vehicle'] = "Negalite leisti sios animacijos budami transporto priemoneje",
    ['in_a_vehicle'] = "Sia animacija galite leisti tik budami transporto priemoneje 🚷",
    ['no_anim_crawling'] = "Narsydami negalite leisti animacijos",
    ['no_anim_right_now'] = "You can't play an animation right now",
    -- Key maps
    ['register_cancel_emote'] = "Atšaukti animaciją",
    ['register_handsup'] = "Pakelti rankas",
    ['register_open_menu'] = "Animacijų meniu",
    ['register_fav_anim'] = "Paleisti mėgstamiausia animaciją",
    ['register_crouch'] = "Pritūpti",
    ['register_crawl'] = "Šliaužti",
    ['register_pointing'] = "Rodyti pirštu",
    ['register_ragdoll'] = "Ragdoll perjungimas",
    -- Commands descriptions
    ['cancel_emote'] = "Atšaukti animaciją",
    ['crouch'] = "Pritūpti",
    ['crawl'] = "Šliaužti",
    ['pointing'] = "Rodyti pirštu"
}
